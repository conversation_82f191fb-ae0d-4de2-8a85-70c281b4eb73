import { PrismaClient } from '@prisma/client';

// Global variable to store the Prisma client instance
let prisma;

// Initialize Prisma client with proper configuration
if (process.env.NODE_ENV === 'production') {
  // In production, create a new instance
  prisma = new PrismaClient({
    log: ['error'],
  });
} else {
  // In development, use a global variable to prevent multiple instances
  // during hot reloads
  if (!global.prisma) {
    global.prisma = new PrismaClient({
      log: ['query', 'error', 'warn'],
    });
  }
  prisma = global.prisma;
}

// Graceful shutdown
process.on('beforeExit', async () => {
  await prisma.$disconnect();
});

export default prisma;
