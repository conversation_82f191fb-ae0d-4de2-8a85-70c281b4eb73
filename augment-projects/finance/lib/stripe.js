import Stripe from 'stripe';

// Initialize Stripe with secret key
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2023-10-16',
});

// Plan configuration with Stripe price IDs and limits
export const PLAN_CONFIG = {
  FREE: {
    name: 'Free',
    price: 0,
    stripePriceId: null,
    limits: {
      scansPerMonth: 5,
      maxFileSize: 1024 * 1024, // 1MB
      features: ['basic-analysis']
    },
    features: [
      'Up to 5 scans per month',
      'Basic vulnerability detection',
      'Gas optimization suggestions',
      'Community support'
    ]
  },
  PRO: {
    name: 'Pro',
    price: 99,
    stripePriceId: process.env.STRIPE_PRO_PRICE_ID,
    limits: {
      scansPerMonth: 100,
      maxFileSize: 5 * 1024 * 1024, // 5MB
      features: ['basic-analysis', 'advanced-analysis', 'api-access', 'priority-support']
    },
    features: [
      'Up to 100 scans per month',
      'Advanced vulnerability detection',
      'Detailed security reports',
      'API access',
      'Priority support',
      'Custom security rules'
    ]
  },
  ENTERPRISE: {
    name: 'Enterprise',
    price: 499,
    stripePriceId: process.env.STRIPE_ENTERPRISE_PRICE_ID,
    limits: {
      scansPerMonth: -1, // unlimited
      maxFileSize: 10 * 1024 * 1024, // 10MB
      features: ['all-features', 'priority-support', 'custom-rules', 'dedicated-support', 'sla']
    },
    features: [
      'Unlimited scans',
      'Enterprise-grade security',
      'Custom compliance frameworks',
      'Dedicated support team',
      'SLA guarantee',
      'On-premise deployment',
      'Custom integrations'
    ]
  }
};

/**
 * Create a Stripe customer
 * @param {Object} user - User object with email and name
 * @returns {Promise<Object>} - Stripe customer object
 */
export async function createStripeCustomer(user) {
  try {
    const customer = await stripe.customers.create({
      email: user.email,
      name: user.name,
      metadata: {
        userId: user.id,
        plan: user.plan
      }
    });
    return customer;
  } catch (error) {
    console.error('Error creating Stripe customer:', error);
    throw error;
  }
}

/**
 * Create a checkout session for subscription
 * @param {string} customerId - Stripe customer ID
 * @param {string} priceId - Stripe price ID
 * @param {string} successUrl - Success redirect URL
 * @param {string} cancelUrl - Cancel redirect URL
 * @returns {Promise<Object>} - Stripe checkout session
 */
export async function createCheckoutSession(customerId, priceId, successUrl, cancelUrl) {
  try {
    const session = await stripe.checkout.sessions.create({
      customer: customerId,
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: successUrl,
      cancel_url: cancelUrl,
      allow_promotion_codes: true,
      billing_address_collection: 'required',
      metadata: {
        customerId: customerId
      }
    });
    return session;
  } catch (error) {
    console.error('Error creating checkout session:', error);
    throw error;
  }
}

/**
 * Create a customer portal session
 * @param {string} customerId - Stripe customer ID
 * @param {string} returnUrl - Return URL after portal session
 * @returns {Promise<Object>} - Stripe portal session
 */
export async function createPortalSession(customerId, returnUrl) {
  try {
    const session = await stripe.billingPortal.sessions.create({
      customer: customerId,
      return_url: returnUrl,
    });
    return session;
  } catch (error) {
    console.error('Error creating portal session:', error);
    throw error;
  }
}

/**
 * Retrieve a subscription from Stripe
 * @param {string} subscriptionId - Stripe subscription ID
 * @returns {Promise<Object>} - Stripe subscription object
 */
export async function getSubscription(subscriptionId) {
  try {
    const subscription = await stripe.subscriptions.retrieve(subscriptionId);
    return subscription;
  } catch (error) {
    console.error('Error retrieving subscription:', error);
    throw error;
  }
}

/**
 * Cancel a subscription
 * @param {string} subscriptionId - Stripe subscription ID
 * @param {boolean} atPeriodEnd - Whether to cancel at period end
 * @returns {Promise<Object>} - Updated subscription object
 */
export async function cancelSubscription(subscriptionId, atPeriodEnd = true) {
  try {
    const subscription = await stripe.subscriptions.update(subscriptionId, {
      cancel_at_period_end: atPeriodEnd,
    });
    return subscription;
  } catch (error) {
    console.error('Error canceling subscription:', error);
    throw error;
  }
}

/**
 * Reactivate a subscription
 * @param {string} subscriptionId - Stripe subscription ID
 * @returns {Promise<Object>} - Updated subscription object
 */
export async function reactivateSubscription(subscriptionId) {
  try {
    const subscription = await stripe.subscriptions.update(subscriptionId, {
      cancel_at_period_end: false,
    });
    return subscription;
  } catch (error) {
    console.error('Error reactivating subscription:', error);
    throw error;
  }
}

/**
 * Construct webhook event from request
 * @param {string} body - Raw request body
 * @param {string} signature - Stripe signature header
 * @returns {Object} - Stripe event object
 */
export function constructWebhookEvent(body, signature) {
  try {
    const event = stripe.webhooks.constructEvent(
      body,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET
    );
    return event;
  } catch (error) {
    console.error('Error constructing webhook event:', error);
    throw error;
  }
}

/**
 * Get plan configuration by plan name
 * @param {string} planName - Plan name (FREE, PRO, ENTERPRISE)
 * @returns {Object} - Plan configuration
 */
export function getPlanConfig(planName) {
  return PLAN_CONFIG[planName] || PLAN_CONFIG.FREE;
}

/**
 * Check if user has exceeded plan limits
 * @param {Object} user - User object with plan and usage data
 * @param {string} limitType - Type of limit to check (scansPerMonth, maxFileSize)
 * @param {number} currentValue - Current value to check against limit
 * @returns {boolean} - Whether limit is exceeded
 */
export function checkPlanLimit(user, limitType, currentValue) {
  const planConfig = getPlanConfig(user.plan);
  const limit = planConfig.limits[limitType];
  
  // -1 means unlimited
  if (limit === -1) return false;
  
  return currentValue >= limit;
}

export default stripe;
