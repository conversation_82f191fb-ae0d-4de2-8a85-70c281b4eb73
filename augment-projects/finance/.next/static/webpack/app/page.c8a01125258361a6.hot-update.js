"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.js":
/*!*********************!*\
  !*** ./app/page.js ***!
  \*********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction Home() {\n    _s();\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadResult, setUploadResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showDemo, setShowDemo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openFaq, setOpenFaq] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        email: '',\n        company: '',\n        message: ''\n    });\n    const handleFileUpload = async (event)=>{\n        const file = event.target.files[0];\n        if (!file) return;\n        setIsUploading(true);\n        setUploadResult(null);\n        try {\n            const formData = new FormData();\n            formData.append('file', file);\n            const response = await fetch('/api/analyze', {\n                method: 'POST',\n                body: formData\n            });\n            const result = await response.json();\n            if (response.ok) {\n                setUploadResult({\n                    fileName: result.fileName,\n                    vulnerabilities: result.vulnerabilities.length,\n                    riskLevel: result.riskLevel,\n                    gasOptimization: result.gasOptimization,\n                    score: result.securityScore,\n                    analysisId: result.analysisId,\n                    detailedResults: result\n                });\n            } else {\n                alert(\"Analysis failed: \".concat(result.error));\n            }\n        } catch (error) {\n            alert('Failed to analyze file. Please try again.');\n            console.error('File upload error:', error);\n        } finally{\n            setIsUploading(false);\n        }\n    };\n    const handleFormSubmit = async (e)=>{\n        e.preventDefault();\n        setIsSubmitting(true);\n        try {\n            const response = await fetch('/api/contact', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    ...formData,\n                    inquiryType: 'general'\n                })\n            });\n            const result = await response.json();\n            if (response.ok) {\n                alert(\"Thank you! \".concat(result.message, \" We'll respond within \").concat(result.responseTime, \".\"));\n                setFormData({\n                    name: '',\n                    email: '',\n                    company: '',\n                    message: ''\n                });\n            } else {\n                alert(\"Error: \".concat(result.error));\n            }\n        } catch (error) {\n            alert('Failed to send message. Please try again.');\n            console.error('Form submission error:', error);\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const scrollToSection = (sectionId)=>{\n        var _document_getElementById;\n        (_document_getElementById = document.getElementById(sectionId)) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.scrollIntoView({\n            behavior: 'smooth'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"container mx-auto px-6 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-bold text-lg\",\n                                        children: \"\\uD83D\\uDEE1️\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                        lineNumber: 100,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-white font-bold text-xl\",\n                                    children: \"SecureAudit\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex space-x-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>scrollToSection('features'),\n                                    className: \"text-gray-300 hover:text-white transition-colors\",\n                                    children: \"Features\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>scrollToSection('pricing'),\n                                    className: \"text-gray-300 hover:text-white transition-colors\",\n                                    children: \"Pricing\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>scrollToSection('demo'),\n                                    className: \"text-gray-300 hover:text-white transition-colors\",\n                                    children: \"Demo\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>scrollToSection('faq'),\n                                    className: \"text-gray-300 hover:text-white transition-colors\",\n                                    children: \"FAQ\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-2 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all\",\n                            children: \"Sign In\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                    lineNumber: 97,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"container mx-auto px-6 py-20 text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-5xl md:text-7xl font-bold text-white mb-6 leading-tight\",\n                            children: [\n                                \"\\uD83D\\uDEE1️ Instantly Secure Your\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent\",\n                                    children: \"Smart Contracts\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, this),\n                                \" \",\n                                \"with AI-Powered Audits\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl md:text-2xl text-gray-300 mb-8 leading-relaxed\",\n                            children: \"Deploy with confidence. Automate vulnerability detection & compliance checks before your code hits the blockchain.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4 justify-center items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>scrollToSection('demo'),\n                                    className: \"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-lg text-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition-all transform hover:scale-105 shadow-lg\",\n                                    children: \"⚡ Get Your Free Code Scan Today\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                    lineNumber: 130,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowDemo(true),\n                                    className: \"border border-gray-400 text-gray-300 px-8 py-4 rounded-lg text-lg font-semibold hover:bg-white hover:text-gray-900 transition-all\",\n                                    children: \"Watch Demo\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                            lineNumber: 129,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"features\",\n                className: \"container mx-auto px-6 py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl font-bold text-white text-center mb-16\",\n                            children: \"Why Choose SecureAudit?\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-slate-800/50 backdrop-blur-sm p-8 rounded-xl border border-slate-700 hover:border-blue-500 transition-all\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-4xl mb-4\",\n                                            children: \"\\uD83D\\uDD0D\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 154,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-white mb-4\",\n                                            children: \"AI-Driven Code Analysis\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 155,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300\",\n                                            children: \"Faster than manual audits, with higher accuracy. Our AI detects vulnerabilities that human auditors might miss.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-slate-800/50 backdrop-blur-sm p-8 rounded-xl border border-slate-700 hover:border-purple-500 transition-all\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-4xl mb-4\",\n                                            children: \"\\uD83D\\uDE80\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 161,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-white mb-4\",\n                                            children: \"Continuous Security Monitoring\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300\",\n                                            children: \"Real-time alerts as your code evolves. Stay protected with ongoing monitoring and instant notifications.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-slate-800/50 backdrop-blur-sm p-8 rounded-xl border border-slate-700 hover:border-green-500 transition-all\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-4xl mb-4\",\n                                            children: \"\\uD83D\\uDD10\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 168,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-white mb-4\",\n                                            children: \"Compliance Reporting\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 169,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300\",\n                                            children: \"Automatic generation of audit reports for regulators & investors. Meet compliance requirements effortlessly.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 170,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                            lineNumber: 152,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                    lineNumber: 148,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                lineNumber: 147,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"container mx-auto px-6 py-20 bg-slate-800/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl font-bold text-white text-center mb-16\",\n                            children: \"Technical Specifications\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-slate-800/50 backdrop-blur-sm p-6 rounded-xl border border-slate-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl mb-4\",\n                                            children: \"⚡\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-bold text-white mb-4\",\n                                            children: \"Languages\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-gray-300 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Solidity (all versions)\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Vyper\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Rust (Solana)\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Move (Aptos/Sui)\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Cairo (StarkNet)\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 189,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-slate-800/50 backdrop-blur-sm p-6 rounded-xl border border-slate-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl mb-4\",\n                                            children: \"\\uD83D\\uDD17\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 200,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-bold text-white mb-4\",\n                                            children: \"Blockchains\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 201,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-gray-300 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Ethereum & L2s\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Polygon, BSC\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Arbitrum, Optimism\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Solana, Avalanche\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• 15+ more networks\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 202,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                    lineNumber: 199,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-slate-800/50 backdrop-blur-sm p-6 rounded-xl border border-slate-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl mb-4\",\n                                            children: \"\\uD83D\\uDEE1️\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 213,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-bold text-white mb-4\",\n                                            children: \"Detects\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 214,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-gray-300 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Reentrancy attacks\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Integer overflow/underflow\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Access control issues\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Logic vulnerabilities\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Gas optimization\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 215,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                    lineNumber: 212,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-slate-800/50 backdrop-blur-sm p-6 rounded-xl border border-slate-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl mb-4\",\n                                            children: \"\\uD83D\\uDD27\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 226,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-bold text-white mb-4\",\n                                            children: \"Integrations\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 227,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-gray-300 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• GitHub Actions\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• VS Code Extension\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• CLI Tools\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• REST API\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Webhook Support\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 228,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                            lineNumber: 184,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-12 grid md:grid-cols-2 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-slate-800/50 backdrop-blur-sm p-8 rounded-xl border border-slate-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-white mb-6 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl mr-3\",\n                                                    children: \"\\uD83D\\uDD12\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Security & Compliance\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 241,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-green-400 font-semibold mb-2\",\n                                                            children: \"Certifications\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                            lineNumber: 247,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"space-y-1 text-gray-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"• SOC2 Type II\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                                    lineNumber: 249,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"• ISO 27001\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                                    lineNumber: 250,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"• GDPR Compliant\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                                    lineNumber: 251,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                            lineNumber: 248,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-blue-400 font-semibold mb-2\",\n                                                            children: \"Security\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                            lineNumber: 255,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"space-y-1 text-gray-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"• End-to-end encryption\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                                    lineNumber: 257,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"• Zero-knowledge analysis\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                                    lineNumber: 258,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"• No code storage\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                                    lineNumber: 259,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                            lineNumber: 256,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 245,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                    lineNumber: 240,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-slate-800/50 backdrop-blur-sm p-8 rounded-xl border border-slate-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-white mb-6 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl mr-3\",\n                                                    children: \"\\uD83D\\uDCCA\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Performance Metrics\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 266,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-purple-400 font-semibold mb-2\",\n                                                            children: \"Speed\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                            lineNumber: 272,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"space-y-1 text-gray-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"• <2s analysis time\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                                    lineNumber: 274,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"• 99.9% uptime SLA\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                                    lineNumber: 275,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"• Real-time results\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                                    lineNumber: 276,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                            lineNumber: 273,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-yellow-400 font-semibold mb-2\",\n                                                            children: \"Accuracy\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                            lineNumber: 280,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"space-y-1 text-gray-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"• 94% detection rate\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                                    lineNumber: 282,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"• <0.1% false positives\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                                    lineNumber: 283,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"• Continuous learning\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                                    lineNumber: 284,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                            lineNumber: 281,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 270,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                    lineNumber: 265,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                            lineNumber: 239,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                    lineNumber: 180,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                lineNumber: 179,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"container mx-auto px-6 py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl font-bold text-white mb-8\",\n                            children: [\n                                \"Cut weeks of manual code review into\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent\",\n                                    children: \"minutes\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                    lineNumber: 298,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                            lineNumber: 296,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-300 mb-12\",\n                            children: \"Reduce costly vulnerabilities and protect your users' assets with our comprehensive security solution.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                            lineNumber: 302,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-3 gap-8 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-blue-400 mb-2\",\n                                            children: \"95%\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 307,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-300\",\n                                            children: \"Faster than manual audits\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 308,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                    lineNumber: 306,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-purple-400 mb-2\",\n                                            children: \"$2M+\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 311,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-300\",\n                                            children: \"In vulnerabilities prevented\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 312,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                    lineNumber: 310,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-green-400 mb-2\",\n                                            children: \"500+\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 315,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-300\",\n                                            children: \"Smart contracts secured\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 316,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                    lineNumber: 314,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                            lineNumber: 305,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                    lineNumber: 295,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                lineNumber: 294,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"container mx-auto px-6 py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl font-bold text-white text-center mb-16\",\n                            children: \"Stop Struggling With Traditional Audits\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                            lineNumber: 325,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-red-500/20 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl\",\n                                                children: \"⏰\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                lineNumber: 331,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 330,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-white mb-4\",\n                                            children: \"Slow & Expensive Manual Audits\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 333,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300\",\n                                            children: \"Traditional audits take weeks and cost thousands. Time is money in the fast-moving Web3 space.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 334,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                    lineNumber: 329,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-red-500/20 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl\",\n                                                children: \"⚠️\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                lineNumber: 340,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 339,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-white mb-4\",\n                                            children: \"Fear of Critical Vulnerabilities\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 342,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300\",\n                                            children: \"One missed vulnerability can cost millions. The stakes are too high to rely on manual reviews alone.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 343,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                    lineNumber: 338,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-red-500/20 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl\",\n                                                children: \"\\uD83D\\uDCCB\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                lineNumber: 349,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 348,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-white mb-4\",\n                                            children: \"Complex Compliance Requirements\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 351,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300\",\n                                            children: \"Navigating regulatory requirements is complex and time-consuming. Miss something and face legal issues.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 352,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                    lineNumber: 347,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                            lineNumber: 328,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                    lineNumber: 324,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                lineNumber: 323,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"container mx-auto px-6 py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-12\",\n                            children: \"Trusted by leading DeFi platforms, NFT marketplaces, and DAOs worldwide\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                            lineNumber: 363,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-4 gap-8 items-center opacity-60\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-slate-700 h-16 rounded-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-bold\",\n                                        children: \"UniSwap\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                        lineNumber: 368,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                    lineNumber: 367,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-slate-700 h-16 rounded-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-bold\",\n                                        children: \"OpenSea\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                        lineNumber: 371,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                    lineNumber: 370,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-slate-700 h-16 rounded-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-bold\",\n                                        children: \"Compound\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                        lineNumber: 374,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                    lineNumber: 373,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-slate-700 h-16 rounded-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-bold\",\n                                        children: \"Aave\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                        lineNumber: 377,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                    lineNumber: 376,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                            lineNumber: 366,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                    lineNumber: 362,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                lineNumber: 361,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"container mx-auto px-6 py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl font-bold text-white text-center mb-16\",\n                            children: \"What Our Customers Say\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                            lineNumber: 386,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-slate-800/50 backdrop-blur-sm p-8 rounded-xl border border-slate-700 hover:border-blue-500 transition-all\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mb-4\",\n                                            children: [\n                                                ...Array(5)\n                                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-yellow-400 text-xl\",\n                                                    children: \"★\"\n                                                }, i, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 394,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 392,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                                            className: \"text-gray-300 mb-6 leading-relaxed\",\n                                            children: '\"SecureAudit caught a critical reentrancy vulnerability that could have cost us $2.3M. Their AI analysis is incredibly thorough and saved our DeFi protocol from a potential disaster.\"'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 397,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mr-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-bold text-lg\",\n                                                        children: \"AS\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                        lineNumber: 402,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 401,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-white font-semibold\",\n                                                            children: \"Alex Chen\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                            lineNumber: 405,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-gray-400 text-sm\",\n                                                            children: \"CTO, DeFiVault Protocol\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                            lineNumber: 406,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 400,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                    lineNumber: 391,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-slate-800/50 backdrop-blur-sm p-8 rounded-xl border border-slate-700 hover:border-purple-500 transition-all\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mb-4\",\n                                            children: [\n                                                ...Array(5)\n                                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-yellow-400 text-xl\",\n                                                    children: \"★\"\n                                                }, i, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 415,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 413,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                                            className: \"text-gray-300 mb-6 leading-relaxed\",\n                                            children: \"\\\"We've been using SecureAudit for 6 months. The real-time monitoring caught 3 vulnerabilities in our smart contracts before deployment. Best investment we've made for security.\\\"\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 418,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-gradient-to-r from-green-500 to-blue-600 rounded-full flex items-center justify-center mr-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-bold text-lg\",\n                                                        children: \"MR\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-white font-semibold\",\n                                                            children: \"Maria Rodriguez\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                            lineNumber: 426,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-gray-400 text-sm\",\n                                                            children: \"Lead Developer, NFT Marketplace\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                            lineNumber: 427,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 425,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 421,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                    lineNumber: 412,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-slate-800/50 backdrop-blur-sm p-8 rounded-xl border border-slate-700 hover:border-green-500 transition-all\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mb-4\",\n                                            children: [\n                                                ...Array(5)\n                                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-yellow-400 text-xl\",\n                                                    children: \"★\"\n                                                }, i, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 436,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 434,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                                            className: \"text-gray-300 mb-6 leading-relaxed\",\n                                            children: '\"The gas optimization suggestions alone saved us 40% on deployment costs. SecureAudit pays for itself and the security insights are invaluable for our enterprise clients.\"'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 439,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full flex items-center justify-center mr-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-bold text-lg\",\n                                                        children: \"DJ\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 443,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-white font-semibold\",\n                                                            children: \"David Kim\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                            lineNumber: 447,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-gray-400 text-sm\",\n                                                            children: \"Security Lead, Enterprise DAO\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                            lineNumber: 448,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 442,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                    lineNumber: 433,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                            lineNumber: 389,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-16 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid md:grid-cols-4 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold text-blue-400 mb-2\",\n                                                children: \"4.9/5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                lineNumber: 458,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-gray-300\",\n                                                children: \"Average Rating\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                lineNumber: 459,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                        lineNumber: 457,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold text-purple-400 mb-2\",\n                                                children: \"1,200+\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                lineNumber: 462,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-gray-300\",\n                                                children: \"Happy Customers\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                lineNumber: 463,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                        lineNumber: 461,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold text-green-400 mb-2\",\n                                                children: \"$50M+\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                lineNumber: 466,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-gray-300\",\n                                                children: \"Assets Protected\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                lineNumber: 467,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                        lineNumber: 465,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold text-yellow-400 mb-2\",\n                                                children: \"99.2%\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                lineNumber: 470,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-gray-300\",\n                                                children: \"Customer Satisfaction\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                lineNumber: 471,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                        lineNumber: 469,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                lineNumber: 456,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                            lineNumber: 455,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                    lineNumber: 385,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                lineNumber: 384,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"pricing\",\n                className: \"container mx-auto px-6 py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl font-bold text-white text-center mb-16\",\n                            children: \"Simple, Transparent Pricing\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                            lineNumber: 481,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-slate-800/50 backdrop-blur-sm p-8 rounded-xl border border-slate-700 hover:border-blue-500 transition-all\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-bold text-white mb-4\",\n                                                children: \"Free Scan\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                lineNumber: 488,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl font-bold text-blue-400 mb-2\",\n                                                children: \"$0\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                lineNumber: 489,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 mb-6\",\n                                                children: \"Perfect for testing\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                lineNumber: 490,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"text-left space-y-3 mb-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center text-gray-300\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-green-400 mr-2\",\n                                                                children: \"✓\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                                lineNumber: 493,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"1 contract scan per month\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                        lineNumber: 492,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center text-gray-300\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-green-400 mr-2\",\n                                                                children: \"✓\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                                lineNumber: 497,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Basic vulnerability detection\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                        lineNumber: 496,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center text-gray-300\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-green-400 mr-2\",\n                                                                children: \"✓\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                                lineNumber: 501,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"PDF report\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                        lineNumber: 500,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center text-gray-300\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-500 mr-2\",\n                                                                children: \"✗\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                                lineNumber: 505,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Real-time monitoring\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                        lineNumber: 504,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                lineNumber: 491,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"w-full bg-blue-600 text-white py-3 rounded-lg hover:bg-blue-700 transition-colors\",\n                                                children: \"Start Free Scan\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                lineNumber: 509,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                        lineNumber: 487,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                    lineNumber: 486,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-b from-purple-600/20 to-blue-600/20 backdrop-blur-sm p-8 rounded-xl border border-purple-500 relative transform scale-105\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -top-4 left-1/2 transform -translate-x-1/2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-gradient-to-r from-purple-600 to-blue-600 text-white px-4 py-1 rounded-full text-sm font-semibold\",\n                                                children: \"Most Popular\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                lineNumber: 518,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 517,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-2xl font-bold text-white mb-4\",\n                                                    children: \"Pro\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 523,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-4xl font-bold text-purple-400 mb-2\",\n                                                    children: \"$99\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 524,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 mb-6\",\n                                                    children: \"per month\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 525,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-left space-y-3 mb-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center text-gray-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-400 mr-2\",\n                                                                    children: \"✓\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                                    lineNumber: 528,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"Unlimited contract scans\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                            lineNumber: 527,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center text-gray-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-400 mr-2\",\n                                                                    children: \"✓\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                                    lineNumber: 532,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"Advanced AI analysis\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                            lineNumber: 531,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center text-gray-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-400 mr-2\",\n                                                                    children: \"✓\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                                    lineNumber: 536,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"Real-time monitoring\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                            lineNumber: 535,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center text-gray-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-400 mr-2\",\n                                                                    children: \"✓\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                                    lineNumber: 540,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"Gas optimization suggestions\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                            lineNumber: 539,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center text-gray-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-400 mr-2\",\n                                                                    children: \"✓\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                                    lineNumber: 544,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"Priority support\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                            lineNumber: 543,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 526,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"w-full bg-gradient-to-r from-purple-600 to-blue-600 text-white py-3 rounded-lg hover:from-purple-700 hover:to-blue-700 transition-all\",\n                                                    children: \"Start Pro Trial\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 548,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 522,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                    lineNumber: 516,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-slate-800/50 backdrop-blur-sm p-8 rounded-xl border border-slate-700 hover:border-green-500 transition-all\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-bold text-white mb-4\",\n                                                children: \"Enterprise\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                lineNumber: 557,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl font-bold text-green-400 mb-2\",\n                                                children: \"Custom\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                lineNumber: 558,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 mb-6\",\n                                                children: \"for large teams\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                lineNumber: 559,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"text-left space-y-3 mb-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center text-gray-300\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-green-400 mr-2\",\n                                                                children: \"✓\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                                lineNumber: 562,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Everything in Pro\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                        lineNumber: 561,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center text-gray-300\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-green-400 mr-2\",\n                                                                children: \"✓\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                                lineNumber: 566,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Custom integrations\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                        lineNumber: 565,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center text-gray-300\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-green-400 mr-2\",\n                                                                children: \"✓\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                                lineNumber: 570,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Dedicated support\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                        lineNumber: 569,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center text-gray-300\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-green-400 mr-2\",\n                                                                children: \"✓\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                                lineNumber: 574,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"SLA guarantees\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                        lineNumber: 573,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center text-gray-300\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-green-400 mr-2\",\n                                                                children: \"✓\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                                lineNumber: 578,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"On-premise deployment\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                        lineNumber: 577,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                lineNumber: 560,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"w-full bg-green-600 text-white py-3 rounded-lg hover:bg-green-700 transition-colors\",\n                                                children: \"Contact Sales\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                lineNumber: 582,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                        lineNumber: 556,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                    lineNumber: 555,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                            lineNumber: 484,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                    lineNumber: 480,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                lineNumber: 479,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"faq\",\n                className: \"container mx-auto px-6 py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl font-bold text-white text-center mb-16\",\n                            children: \"Frequently Asked Questions\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                            lineNumber: 594,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                {\n                                    question: \"How secure is your AI analysis platform?\",\n                                    answer: \"Our platform uses enterprise-grade security with SOC2 Type II compliance, end-to-end encryption, and zero-knowledge architecture. Your smart contracts are analyzed in isolated environments and never stored permanently. We're audited by leading cybersecurity firms and maintain the highest industry standards.\"\n                                },\n                                {\n                                    question: \"What smart contract languages and blockchains do you support?\",\n                                    answer: \"We support Solidity, Vyper, Rust (for Solana), and Move (for Aptos/Sui). Our platform works with Ethereum, Polygon, BSC, Arbitrum, Optimism, Solana, and 15+ other major blockchains. We're constantly adding support for new languages and networks based on community demand.\"\n                                },\n                                {\n                                    question: \"How accurate is your AI compared to manual audits?\",\n                                    answer: \"Our AI achieves 94% accuracy in vulnerability detection, compared to 78% for manual audits alone. We combine multiple AI models with rule-based analysis and have been trained on over 100,000 audited contracts. However, we recommend combining our AI analysis with expert review for critical applications.\"\n                                },\n                                {\n                                    question: \"What happens after my free scan?\",\n                                    answer: \"After your free scan, you'll receive a detailed PDF report with vulnerability findings, risk assessment, and gas optimization suggestions. You can upgrade to Pro for unlimited scans, real-time monitoring, and priority support. No credit card required for the free tier.\"\n                                },\n                                {\n                                    question: \"How does this integrate with my development workflow?\",\n                                    answer: \"We offer multiple integration options: GitHub Actions for CI/CD, CLI tools for local development, VS Code extension, and REST API for custom integrations. You can set up automated scanning on every commit, pull request, or deployment.\"\n                                },\n                                {\n                                    question: \"Do you provide compliance reports for regulators?\",\n                                    answer: \"Yes, our Pro and Enterprise plans include compliance reporting for SOX, PCI DSS, and emerging DeFi regulations. Reports include executive summaries, technical findings, remediation timelines, and audit trails suitable for regulatory submission.\"\n                                },\n                                {\n                                    question: \"What's your SLA for Enterprise customers?\",\n                                    answer: \"Enterprise customers receive 99.9% uptime SLA, <2 second analysis response times, 24/7 dedicated support, and guaranteed vulnerability detection accuracy. We also offer on-premise deployment and custom integration support.\"\n                                },\n                                {\n                                    question: \"Can you detect zero-day vulnerabilities?\",\n                                    answer: \"Our AI models are continuously updated with the latest vulnerability patterns and can detect novel attack vectors by analyzing code patterns and logic flows. We've successfully identified several zero-day vulnerabilities before they were publicly disclosed.\"\n                                }\n                            ].map((faq, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700 overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setOpenFaq(openFaq === index ? null : index),\n                                            className: \"w-full px-8 py-6 text-left flex justify-between items-center hover:bg-slate-700/30 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-white pr-4\",\n                                                    children: faq.question\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 637,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl text-blue-400 transition-transform \".concat(openFaq === index ? 'rotate-45' : ''),\n                                                    children: \"+\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 638,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 633,\n                                            columnNumber: 17\n                                        }, this),\n                                        openFaq === index && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-8 pb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-300 leading-relaxed\",\n                                                children: faq.answer\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                lineNumber: 644,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 643,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                    lineNumber: 632,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                            lineNumber: 597,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mt-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 mb-4\",\n                                    children: \"Still have questions?\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                    lineNumber: 652,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>scrollToSection('demo'),\n                                    className: \"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all\",\n                                    children: \"Contact Our Security Experts\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                    lineNumber: 653,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                            lineNumber: 651,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                    lineNumber: 593,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                lineNumber: 592,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"demo\",\n                className: \"container mx-auto px-6 py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl font-bold text-white text-center mb-8\",\n                            children: \"Try Our AI-Powered Smart Contract Scanner\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                            lineNumber: 666,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-300 text-center mb-12\",\n                            children: \"Upload your smart contract and get instant security analysis\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                            lineNumber: 669,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-slate-800/50 backdrop-blur-sm p-8 rounded-xl border border-slate-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border-2 border-dashed border-gray-600 rounded-lg p-12 mb-6 hover:border-blue-500 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"file\",\n                                                accept: \".sol,.js,.ts\",\n                                                onChange: handleFileUpload,\n                                                className: \"hidden\",\n                                                id: \"file-upload\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                lineNumber: 676,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"file-upload\",\n                                                className: \"cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-6xl mb-4\",\n                                                        children: \"\\uD83D\\uDCC4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                        lineNumber: 684,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl text-white mb-2\",\n                                                        children: \"Drop your smart contract here\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                        lineNumber: 685,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400\",\n                                                        children: \"or click to browse (.sol, .js, .ts files)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                        lineNumber: 686,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                lineNumber: 683,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                        lineNumber: 675,\n                                        columnNumber: 15\n                                    }, this),\n                                    isUploading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                lineNumber: 692,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white\",\n                                                children: \"Analyzing your smart contract...\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                lineNumber: 693,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: \"This may take a few moments\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                lineNumber: 694,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                        lineNumber: 691,\n                                        columnNumber: 17\n                                    }, this),\n                                    uploadResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-slate-700/50 rounded-lg p-6 text-left\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-bold text-white mb-4\",\n                                                children: [\n                                                    \"\\uD83D\\uDCCA Analysis Results for \",\n                                                    uploadResult.fileName\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                lineNumber: 700,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid md:grid-cols-2 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-400\",\n                                                                        children: \"Security Score:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                                        lineNumber: 706,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center mt-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"bg-gray-700 rounded-full h-2 flex-1 mr-3\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"bg-gradient-to-r from-green-500 to-blue-500 h-2 rounded-full\",\n                                                                                    style: {\n                                                                                        width: \"\".concat(uploadResult.score, \"%\")\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                                                    lineNumber: 709,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                                                lineNumber: 708,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-white font-bold\",\n                                                                                children: [\n                                                                                    uploadResult.score,\n                                                                                    \"/100\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                                                lineNumber: 714,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                                        lineNumber: 707,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                                lineNumber: 705,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-400\",\n                                                                        children: \"Vulnerabilities Found:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                                        lineNumber: 718,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2 font-bold \".concat(uploadResult.vulnerabilities > 2 ? 'text-red-400' : uploadResult.vulnerabilities > 0 ? 'text-yellow-400' : 'text-green-400'),\n                                                                        children: uploadResult.vulnerabilities\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                                        lineNumber: 719,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                                lineNumber: 717,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                        lineNumber: 704,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-400\",\n                                                                        children: \"Risk Level:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                                        lineNumber: 726,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2 font-bold \".concat(uploadResult.riskLevel === 'High' ? 'text-red-400' : uploadResult.riskLevel === 'Medium' ? 'text-yellow-400' : 'text-green-400'),\n                                                                        children: uploadResult.riskLevel\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                                        lineNumber: 727,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                                lineNumber: 725,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-400\",\n                                                                        children: \"Gas Optimization:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                                        lineNumber: 732,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2 font-bold text-blue-400\",\n                                                                        children: [\n                                                                            uploadResult.gasOptimization,\n                                                                            \"% savings possible\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                                        lineNumber: 733,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                                lineNumber: 731,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                        lineNumber: 724,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                lineNumber: 703,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-6 pt-6 border-t border-slate-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all mr-4\",\n                                                        children: \"Download Full Report\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                        lineNumber: 738,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"border border-gray-400 text-gray-300 px-6 py-3 rounded-lg hover:bg-white hover:text-gray-900 transition-all\",\n                                                        children: \"Schedule Expert Review\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                        lineNumber: 741,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                lineNumber: 737,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                        lineNumber: 699,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                lineNumber: 674,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                            lineNumber: 673,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                    lineNumber: 665,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                lineNumber: 664,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"container mx-auto px-6 py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-2xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl font-bold text-white text-center mb-8\",\n                            children: \"Request a Demo\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                            lineNumber: 755,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-300 text-center mb-12\",\n                            children: \"Get a personalized demo and see how SecureAudit can protect your smart contracts\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                            lineNumber: 758,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleFormSubmit,\n                            className: \"bg-slate-800/50 backdrop-blur-sm p-8 rounded-xl border border-slate-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid md:grid-cols-2 gap-6 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-white mb-2\",\n                                                    children: \"Name *\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 765,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    required: true,\n                                                    value: formData.name,\n                                                    onChange: (e)=>setFormData({\n                                                            ...formData,\n                                                            name: e.target.value\n                                                        }),\n                                                    className: \"w-full bg-slate-700 text-white px-4 py-3 rounded-lg border border-slate-600 focus:border-blue-500 focus:outline-none\",\n                                                    placeholder: \"Your full name\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 766,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 764,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-white mb-2\",\n                                                    children: \"Email *\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 776,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"email\",\n                                                    required: true,\n                                                    value: formData.email,\n                                                    onChange: (e)=>setFormData({\n                                                            ...formData,\n                                                            email: e.target.value\n                                                        }),\n                                                    className: \"w-full bg-slate-700 text-white px-4 py-3 rounded-lg border border-slate-600 focus:border-blue-500 focus:outline-none\",\n                                                    placeholder: \"<EMAIL>\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 777,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 775,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                    lineNumber: 763,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-white mb-2\",\n                                            children: \"Company\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 788,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: formData.company,\n                                            onChange: (e)=>setFormData({\n                                                    ...formData,\n                                                    company: e.target.value\n                                                }),\n                                            className: \"w-full bg-slate-700 text-white px-4 py-3 rounded-lg border border-slate-600 focus:border-blue-500 focus:outline-none\",\n                                            placeholder: \"Your company name\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 789,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                    lineNumber: 787,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-white mb-2\",\n                                            children: \"Message\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 798,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            rows: \"4\",\n                                            value: formData.message,\n                                            onChange: (e)=>setFormData({\n                                                    ...formData,\n                                                    message: e.target.value\n                                                }),\n                                            className: \"w-full bg-slate-700 text-white px-4 py-3 rounded-lg border border-slate-600 focus:border-blue-500 focus:outline-none\",\n                                            placeholder: \"Tell us about your project and security needs...\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 799,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                    lineNumber: 797,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    className: \"w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-4 rounded-lg text-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition-all\",\n                                    children: \"Request Demo\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                    lineNumber: 807,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                            lineNumber: 762,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                    lineNumber: 754,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                lineNumber: 753,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"container mx-auto px-6 py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto text-center bg-gradient-to-r from-blue-600/20 to-purple-600/20 backdrop-blur-sm p-12 rounded-2xl border border-slate-700\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl font-bold text-white mb-6\",\n                            children: \"Ready to Secure Your Smart Contracts?\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                            lineNumber: 820,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-300 mb-8\",\n                            children: \"Join hundreds of developers who trust SecureAudit to protect their code and users.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                            lineNumber: 823,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>scrollToSection('demo'),\n                            className: \"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-12 py-4 rounded-lg text-xl font-semibold hover:from-blue-700 hover:to-purple-700 transition-all transform hover:scale-105 shadow-lg\",\n                            children: \"Scan My Contract Now\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                            lineNumber: 826,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-400 mt-4\",\n                            children: \"Free scan • No credit card required • Results in minutes\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                            lineNumber: 832,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                    lineNumber: 819,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                lineNumber: 818,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"container mx-auto px-6 py-12 border-t border-slate-700\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 mb-4 md:mb-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-bold text-lg\",\n                                            children: \"\\uD83D\\uDEE1️\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 843,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                        lineNumber: 842,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-bold text-xl\",\n                                        children: \"SecureAudit\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                        lineNumber: 845,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                lineNumber: 841,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-6 text-gray-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/privacy\",\n                                        className: \"hover:text-white transition-colors\",\n                                        children: \"Privacy\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                        lineNumber: 848,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/terms\",\n                                        className: \"hover:text-white transition-colors\",\n                                        children: \"Terms\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                        lineNumber: 849,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/contact\",\n                                        className: \"hover:text-white transition-colors\",\n                                        children: \"Contact\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                        lineNumber: 850,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/about\",\n                                        className: \"hover:text-white transition-colors\",\n                                        children: \"About\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                        lineNumber: 851,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                lineNumber: 847,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                        lineNumber: 840,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center text-gray-400 mt-8\",\n                        children: \"\\xa9 2024 SecureAudit. All rights reserved.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                        lineNumber: 854,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                lineNumber: 839,\n                columnNumber: 7\n            }, this),\n            showDemo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-slate-800 rounded-xl p-8 max-w-2xl w-full max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-white\",\n                                    children: \"SecureAudit Demo\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                    lineNumber: 864,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowDemo(false),\n                                    className: \"text-gray-400 hover:text-white text-2xl\",\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                    lineNumber: 865,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                            lineNumber: 863,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"aspect-video bg-slate-700 rounded-lg mb-6 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-6xl mb-4\",\n                                        children: \"\\uD83C\\uDFA5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                        lineNumber: 875,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white text-lg mb-2\",\n                                        children: \"Demo Video\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                        lineNumber: 876,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400\",\n                                        children: \"See SecureAudit in action\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                        lineNumber: 877,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"mt-4 bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\",\n                                        children: \"▶ Play Demo\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                        lineNumber: 878,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                lineNumber: 874,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                            lineNumber: 873,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold text-white\",\n                                    children: \"What you'll see in this demo:\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                    lineNumber: 885,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 text-gray-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-400 mr-2\",\n                                                    children: \"✓\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 888,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Upload and scan a real smart contract\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 887,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-400 mr-2\",\n                                                    children: \"✓\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 892,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"AI-powered vulnerability detection in action\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 891,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-400 mr-2\",\n                                                    children: \"✓\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 896,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Detailed security report generation\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 895,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-400 mr-2\",\n                                                    children: \"✓\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 900,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Gas optimization recommendations\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 899,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-400 mr-2\",\n                                                    children: \"✓\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                                    lineNumber: 904,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Real-time monitoring dashboard\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                            lineNumber: 903,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                    lineNumber: 886,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                            lineNumber: 884,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8 flex gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        setShowDemo(false);\n                                        scrollToSection('demo');\n                                    },\n                                    className: \"flex-1 bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all\",\n                                    children: \"Try Live Demo\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                    lineNumber: 911,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowDemo(false),\n                                    className: \"flex-1 border border-gray-400 text-gray-300 py-3 rounded-lg hover:bg-white hover:text-gray-900 transition-all\",\n                                    children: \"Close\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                                    lineNumber: 920,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                            lineNumber: 910,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                    lineNumber: 862,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n                lineNumber: 861,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/finance/app/page.js\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"YZYqHilEjeX7KYxGEpUFLXLeer4=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.js\n"));

/***/ })

});