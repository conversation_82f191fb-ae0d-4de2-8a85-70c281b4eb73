/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/register/route";
exports.ids = ["app/api/register/route"];
exports.modules = {

/***/ "(rsc)/./app/api/register/route.js":
/*!***********************************!*\
  !*** ./app/api/register/route.js ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_db_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../lib/db.js */ \"(rsc)/./lib/db.js\");\n/* harmony import */ var _lib_auth_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../lib/auth.js */ \"(rsc)/./lib/auth.js\");\n\n\n\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { name, email, password, company, plan } = body;\n        // Validate required fields\n        if (!name || !email || !password) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Name, email, and password are required'\n            }, {\n                status: 400\n            });\n        }\n        // Validate email format\n        if (!(0,_lib_auth_js__WEBPACK_IMPORTED_MODULE_2__.isValidEmail)(email)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid email format'\n            }, {\n                status: 400\n            });\n        }\n        // Validate password strength\n        const passwordValidation = (0,_lib_auth_js__WEBPACK_IMPORTED_MODULE_2__.validatePassword)(password);\n        if (!passwordValidation.isValid) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: passwordValidation.errors[0]\n            }, {\n                status: 400\n            });\n        }\n        // Check if user already exists\n        const existingUser = await _lib_db_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"].user.findUnique({\n            where: {\n                email: email.toLowerCase()\n            }\n        });\n        if (existingUser) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'User with this email already exists'\n            }, {\n                status: 409\n            });\n        }\n        // Hash password\n        const hashedPassword = await (0,_lib_auth_js__WEBPACK_IMPORTED_MODULE_2__.hashPassword)(password);\n        // Determine plan (convert string to enum)\n        let userPlan = 'FREE';\n        if (plan) {\n            const planUpper = plan.toUpperCase();\n            if ([\n                'FREE',\n                'PRO',\n                'ENTERPRISE'\n            ].includes(planUpper)) {\n                userPlan = planUpper;\n            }\n        }\n        // Create user in database\n        const user = await _lib_db_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"].user.create({\n            data: {\n                name,\n                email: email.toLowerCase(),\n                password: hashedPassword,\n                company: company || null,\n                plan: userPlan\n            }\n        });\n        // Generate JWT token and create session\n        const token = (0,_lib_auth_js__WEBPACK_IMPORTED_MODULE_2__.generateToken)(user);\n        await (0,_lib_auth_js__WEBPACK_IMPORTED_MODULE_2__.createSession)(user.id, token);\n        // Log registration for monitoring\n        console.log('User registration:', {\n            timestamp: new Date().toISOString(),\n            userId: user.id,\n            email: user.email,\n            plan: user.plan\n        });\n        // Send welcome email (in production)\n        if (false) {}\n        // Return success response (don't include password)\n        const { password: _, ...userResponse } = user;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Account created successfully!',\n            user: userResponse,\n            token,\n            nextSteps: {\n                dashboard: '/dashboard',\n                firstScan: '/dashboard/scan',\n                documentation: '/docs'\n            }\n        });\n    } catch (error) {\n        console.error('Registration error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to create account. Please try again.'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET(request) {\n    // Get user count for admin purposes\n    const url = new URL(request.url);\n    const adminKey = url.searchParams.get('admin_key');\n    if (adminKey !== process.env.ADMIN_KEY) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Unauthorized'\n        }, {\n            status: 401\n        });\n    }\n    try {\n        // Get user statistics from database\n        const totalUsers = await _lib_db_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"].user.count();\n        const planDistribution = await _lib_db_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"].user.groupBy({\n            by: [\n                'plan'\n            ],\n            _count: {\n                plan: true\n            }\n        });\n        const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);\n        const recentRegistrations = await _lib_db_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"].user.count({\n            where: {\n                createdAt: {\n                    gte: oneDayAgo\n                }\n            }\n        });\n        const userStats = {\n            totalUsers,\n            planDistribution: {\n                FREE: 0,\n                PRO: 0,\n                ENTERPRISE: 0\n            },\n            recentRegistrations\n        };\n        // Format plan distribution\n        planDistribution.forEach((item)=>{\n            userStats.planDistribution[item.plan] = item._count.plan;\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(userStats);\n    } catch (error) {\n        console.error('Error fetching user stats:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch user statistics'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function sendWelcomeEmail(user) {\n    // In production, implement actual email sending\n    const welcomeEmailContent = {\n        to: user.email,\n        subject: 'Welcome to SecureAudit - Your Smart Contract Security Journey Begins!',\n        html: `\n      <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n        <div style=\"background: linear-gradient(135deg, #1e293b 0%, #7c3aed 100%); padding: 20px; text-align: center;\">\n          <h1 style=\"color: white; margin: 0;\">🛡️ Welcome to SecureAudit!</h1>\n        </div>\n        \n        <div style=\"padding: 30px; background: #f8fafc; border: 1px solid #e2e8f0;\">\n          <h2 style=\"color: #1e293b; margin-top: 0;\">Hi ${user.name},</h2>\n          \n          <p style=\"color: #374151; line-height: 1.6;\">\n            Welcome to SecureAudit! We're excited to help you secure your smart contracts with our AI-powered analysis platform.\n          </p>\n          \n          <div style=\"background: #dbeafe; padding: 20px; border-radius: 8px; margin: 20px 0;\">\n            <h3 style=\"color: #1e40af; margin: 0 0 15px 0;\">Your Account Details</h3>\n            <p style=\"color: #1e40af; margin: 0;\"><strong>Plan:</strong> ${user.plan.charAt(0).toUpperCase() + user.plan.slice(1)}</p>\n            <p style=\"color: #1e40af; margin: 5px 0 0 0;\"><strong>Email:</strong> ${user.email}</p>\n          </div>\n          \n          <h3 style=\"color: #1e293b;\">Next Steps:</h3>\n          <ol style=\"color: #374151; line-height: 1.6;\">\n            <li>Visit your <a href=\"https://secureaudit.com/dashboard\" style=\"color: #3b82f6;\">dashboard</a></li>\n            <li>Upload your first smart contract for analysis</li>\n            <li>Explore our <a href=\"https://secureaudit.com/docs\" style=\"color: #3b82f6;\">documentation</a></li>\n            <li>Set up integrations with your development workflow</li>\n          </ol>\n          \n          <div style=\"background: #f0fdf4; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #22c55e;\">\n            <h4 style=\"color: #15803d; margin: 0 0 10px 0;\">🎉 Special Welcome Offer</h4>\n            <p style=\"color: #15803d; margin: 0; font-size: 14px;\">\n              As a new user, you get <strong>3 free premium scans</strong> to try our advanced features!\n            </p>\n          </div>\n          \n          <p style=\"color: #374151; line-height: 1.6;\">\n            If you have any questions, our support team is here to help at \n            <a href=\"mailto:<EMAIL>\" style=\"color: #3b82f6;\"><EMAIL></a>\n          </p>\n          \n          <p style=\"color: #374151; line-height: 1.6;\">\n            Best regards,<br>\n            <strong>The SecureAudit Team</strong>\n          </p>\n        </div>\n        \n        <div style=\"padding: 20px; text-align: center; background: #1e293b;\">\n          <p style=\"color: #94a3b8; margin: 0 0 10px 0; font-size: 14px;\">\n            <strong>SecureAudit</strong> - AI-Powered Smart Contract Security\n          </p>\n          <p style=\"color: #64748b; margin: 0; font-size: 12px;\">\n            123 Security Blvd, San Francisco, CA 94105\n          </p>\n        </div>\n      </div>\n    `\n    };\n    console.log('Welcome email would be sent:', welcomeEmailContent);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/register/route.js\n");

/***/ }),

/***/ "(rsc)/./lib/auth.js":
/*!*********************!*\
  !*** ./lib/auth.js ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cleanupExpiredSessions: () => (/* binding */ cleanupExpiredSessions),\n/* harmony export */   createSession: () => (/* binding */ createSession),\n/* harmony export */   generateToken: () => (/* binding */ generateToken),\n/* harmony export */   getUserFromToken: () => (/* binding */ getUserFromToken),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   invalidateSession: () => (/* binding */ invalidateSession),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   validatePassword: () => (/* binding */ validatePassword),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _db_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./db.js */ \"(rsc)/./lib/db.js\");\n\n\n\n// JWT Configuration\nconst JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-change-in-production';\nconst JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';\n/**\n * Hash a password using bcrypt\n * @param {string} password - Plain text password\n * @returns {Promise<string>} - Hashed password\n */ async function hashPassword(password) {\n    const saltRounds = 12;\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_0___default().hash(password, saltRounds);\n}\n/**\n * Verify a password against its hash\n * @param {string} password - Plain text password\n * @param {string} hashedPassword - Hashed password from database\n * @returns {Promise<boolean>} - True if password matches\n */ async function verifyPassword(password, hashedPassword) {\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_0___default().compare(password, hashedPassword);\n}\n/**\n * Generate a JWT token for a user\n * @param {Object} user - User object with id and email\n * @returns {string} - JWT token\n */ function generateToken(user) {\n    const payload = {\n        userId: user.id,\n        email: user.email,\n        plan: user.plan,\n        iat: Math.floor(Date.now() / 1000)\n    };\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, JWT_SECRET, {\n        expiresIn: JWT_EXPIRES_IN\n    });\n}\n/**\n * Verify and decode a JWT token\n * @param {string} token - JWT token\n * @returns {Object|null} - Decoded token payload or null if invalid\n */ function verifyToken(token) {\n    try {\n        return jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, JWT_SECRET);\n    } catch (error) {\n        console.error('Token verification failed:', error.message);\n        return null;\n    }\n}\n/**\n * Create a session in the database\n * @param {string} userId - User ID\n * @param {string} token - JWT token\n * @returns {Promise<Object>} - Created session\n */ async function createSession(userId, token) {\n    // Calculate expiration date (7 days from now)\n    const expiresAt = new Date();\n    expiresAt.setDate(expiresAt.getDate() + 7);\n    return await _db_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.create({\n        data: {\n            userId,\n            token,\n            expiresAt\n        }\n    });\n}\n/**\n * Get user from token\n * @param {string} token - JWT token\n * @returns {Promise<Object|null>} - User object or null\n */ async function getUserFromToken(token) {\n    try {\n        const decoded = verifyToken(token);\n        if (!decoded) return null;\n        // Check if session exists and is valid\n        const session = await _db_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.findUnique({\n            where: {\n                token\n            },\n            include: {\n                user: true\n            }\n        });\n        if (!session || session.expiresAt < new Date()) {\n            // Clean up expired session\n            if (session) {\n                await _db_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.delete({\n                    where: {\n                        id: session.id\n                    }\n                });\n            }\n            return null;\n        }\n        return session.user;\n    } catch (error) {\n        console.error('Error getting user from token:', error);\n        return null;\n    }\n}\n/**\n * Invalidate a session (logout)\n * @param {string} token - JWT token\n * @returns {Promise<boolean>} - True if session was deleted\n */ async function invalidateSession(token) {\n    try {\n        await _db_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.delete({\n            where: {\n                token\n            }\n        });\n        return true;\n    } catch (error) {\n        console.error('Error invalidating session:', error);\n        return false;\n    }\n}\n/**\n * Clean up expired sessions\n * @returns {Promise<number>} - Number of deleted sessions\n */ async function cleanupExpiredSessions() {\n    try {\n        const result = await _db_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.deleteMany({\n            where: {\n                expiresAt: {\n                    lt: new Date()\n                }\n            }\n        });\n        return result.count;\n    } catch (error) {\n        console.error('Error cleaning up expired sessions:', error);\n        return 0;\n    }\n}\n/**\n * Validate email format\n * @param {string} email - Email address\n * @returns {boolean} - True if email is valid\n */ function isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\n/**\n * Validate password strength\n * @param {string} password - Password\n * @returns {Object} - Validation result with isValid and errors\n */ function validatePassword(password) {\n    const errors = [];\n    if (password.length < 8) {\n        errors.push('Password must be at least 8 characters long');\n    }\n    if (!/[A-Z]/.test(password)) {\n        errors.push('Password must contain at least one uppercase letter');\n    }\n    if (!/[a-z]/.test(password)) {\n        errors.push('Password must contain at least one lowercase letter');\n    }\n    if (!/\\d/.test(password)) {\n        errors.push('Password must contain at least one number');\n    }\n    return {\n        isValid: errors.length === 0,\n        errors\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.js\n");

/***/ }),

/***/ "(rsc)/./lib/db.js":
/*!*******************!*\
  !*** ./lib/db.js ***!
  \*******************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\n// Global variable to store the Prisma client instance\nlet prisma;\n// Initialize Prisma client with proper configuration\nif (false) {} else {\n    // In development, use a global variable to prevent multiple instances\n    // during hot reloads\n    if (!global.prisma) {\n        global.prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n            log: [\n                'query',\n                'error',\n                'warn'\n            ]\n        });\n    }\n    prisma = global.prisma;\n}\n// Graceful shutdown\nprocess.on('beforeExit', async ()=>{\n    await prisma.$disconnect();\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (prisma);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvZGIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQThDO0FBRTlDLHNEQUFzRDtBQUN0RCxJQUFJQztBQUVKLHFEQUFxRDtBQUNyRCxJQUFJQyxLQUFxQyxFQUFFLEVBSzFDLE1BQU07SUFDTCxzRUFBc0U7SUFDdEUscUJBQXFCO0lBQ3JCLElBQUksQ0FBQ0UsT0FBT0gsTUFBTSxFQUFFO1FBQ2xCRyxPQUFPSCxNQUFNLEdBQUcsSUFBSUQsd0RBQVlBLENBQUM7WUFDL0JHLEtBQUs7Z0JBQUM7Z0JBQVM7Z0JBQVM7YUFBTztRQUNqQztJQUNGO0lBQ0FGLFNBQVNHLE9BQU9ILE1BQU07QUFDeEI7QUFFQSxvQkFBb0I7QUFDcEJDLFFBQVFHLEVBQUUsQ0FBQyxjQUFjO0lBQ3ZCLE1BQU1KLE9BQU9LLFdBQVc7QUFDMUI7QUFFQSxpRUFBZUwsTUFBTUEsRUFBQyIsInNvdXJjZXMiOlsiL1VzZXJzL2dpb3JnaS9Eb2N1bWVudHMvYXVnbWVudC1wcm9qZWN0cy9maW5hbmNlL2xpYi9kYi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCc7XG5cbi8vIEdsb2JhbCB2YXJpYWJsZSB0byBzdG9yZSB0aGUgUHJpc21hIGNsaWVudCBpbnN0YW5jZVxubGV0IHByaXNtYTtcblxuLy8gSW5pdGlhbGl6ZSBQcmlzbWEgY2xpZW50IHdpdGggcHJvcGVyIGNvbmZpZ3VyYXRpb25cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIC8vIEluIHByb2R1Y3Rpb24sIGNyZWF0ZSBhIG5ldyBpbnN0YW5jZVxuICBwcmlzbWEgPSBuZXcgUHJpc21hQ2xpZW50KHtcbiAgICBsb2c6IFsnZXJyb3InXSxcbiAgfSk7XG59IGVsc2Uge1xuICAvLyBJbiBkZXZlbG9wbWVudCwgdXNlIGEgZ2xvYmFsIHZhcmlhYmxlIHRvIHByZXZlbnQgbXVsdGlwbGUgaW5zdGFuY2VzXG4gIC8vIGR1cmluZyBob3QgcmVsb2Fkc1xuICBpZiAoIWdsb2JhbC5wcmlzbWEpIHtcbiAgICBnbG9iYWwucHJpc21hID0gbmV3IFByaXNtYUNsaWVudCh7XG4gICAgICBsb2c6IFsncXVlcnknLCAnZXJyb3InLCAnd2FybiddLFxuICAgIH0pO1xuICB9XG4gIHByaXNtYSA9IGdsb2JhbC5wcmlzbWE7XG59XG5cbi8vIEdyYWNlZnVsIHNodXRkb3duXG5wcm9jZXNzLm9uKCdiZWZvcmVFeGl0JywgYXN5bmMgKCkgPT4ge1xuICBhd2FpdCBwcmlzbWEuJGRpc2Nvbm5lY3QoKTtcbn0pO1xuXG5leHBvcnQgZGVmYXVsdCBwcmlzbWE7XG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwicHJpc21hIiwicHJvY2VzcyIsImxvZyIsImdsb2JhbCIsIm9uIiwiJGRpc2Nvbm5lY3QiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./lib/db.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fregister%2Froute&page=%2Fapi%2Fregister%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fregister%2Froute.js&appDir=%2FUsers%2Fgiorgi%2FDocuments%2Faugment-projects%2Ffinance%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fgiorgi%2FDocuments%2Faugment-projects%2Ffinance&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fregister%2Froute&page=%2Fapi%2Fregister%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fregister%2Froute.js&appDir=%2FUsers%2Fgiorgi%2FDocuments%2Faugment-projects%2Ffinance%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fgiorgi%2FDocuments%2Faugment-projects%2Ffinance&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_giorgi_Documents_augment_projects_finance_app_api_register_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/register/route.js */ \"(rsc)/./app/api/register/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/register/route\",\n        pathname: \"/api/register\",\n        filename: \"route\",\n        bundlePath: \"app/api/register/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/augment-projects/finance/app/api/register/route.js\",\n    nextConfigOutput,\n    userland: _Users_giorgi_Documents_augment_projects_finance_app_api_register_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fregister%2Froute&page=%2Fapi%2Fregister%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fregister%2Froute.js&appDir=%2FUsers%2Fgiorgi%2FDocuments%2Faugment-projects%2Ffinance%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fgiorgi%2FDocuments%2Faugment-projects%2Ffinance&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fregister%2Froute&page=%2Fapi%2Fregister%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fregister%2Froute.js&appDir=%2FUsers%2Fgiorgi%2FDocuments%2Faugment-projects%2Ffinance%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fgiorgi%2FDocuments%2Faugment-projects%2Ffinance&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();