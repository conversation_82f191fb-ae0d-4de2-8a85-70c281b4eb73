/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/subscription/status/route";
exports.ids = ["app/api/subscription/status/route"];
exports.modules = {

/***/ "(rsc)/./app/api/subscription/status/route.js":
/*!**********************************************!*\
  !*** ./app/api/subscription/status/route.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../lib/auth.js */ \"(rsc)/./lib/auth.js\");\n/* harmony import */ var _lib_stripe_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../../lib/stripe.js */ \"(rsc)/./lib/stripe.js\");\n/* harmony import */ var _lib_db_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../lib/db.js */ \"(rsc)/./lib/db.js\");\n\n\n\n\nasync function GET(request) {\n    try {\n        // Get user from token\n        const authHeader = request.headers.get('authorization');\n        if (!authHeader || !authHeader.startsWith('Bearer ')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Authentication required'\n            }, {\n                status: 401\n            });\n        }\n        const token = authHeader.substring(7);\n        const user = await (0,_lib_auth_js__WEBPACK_IMPORTED_MODULE_1__.getUserFromToken)(token);\n        if (!user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid or expired token'\n            }, {\n                status: 401\n            });\n        }\n        // Get user with subscription details\n        const userWithSubscription = await _lib_db_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"].user.findUnique({\n            where: {\n                id: user.id\n            },\n            include: {\n                subscription: true,\n                payments: {\n                    orderBy: {\n                        createdAt: 'desc'\n                    },\n                    take: 5\n                }\n            }\n        });\n        if (!userWithSubscription) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'User not found'\n            }, {\n                status: 404\n            });\n        }\n        // Get plan configuration\n        const planConfig = (0,_lib_stripe_js__WEBPACK_IMPORTED_MODULE_2__.getPlanConfig)(userWithSubscription.plan);\n        // Calculate usage for current month\n        const now = new Date();\n        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);\n        const monthlyScans = await _lib_db_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"].scan.count({\n            where: {\n                userId: user.id,\n                createdAt: {\n                    gte: startOfMonth\n                }\n            }\n        });\n        // Check if user needs to reset monthly scan count\n        const lastReset = new Date(userWithSubscription.lastScanReset);\n        const shouldReset = lastReset < startOfMonth;\n        if (shouldReset) {\n            await _lib_db_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"].user.update({\n                where: {\n                    id: user.id\n                },\n                data: {\n                    monthlyScans: monthlyScans,\n                    lastScanReset: now\n                }\n            });\n        }\n        // Calculate limits and usage\n        const scansLimit = planConfig.limits.scansPerMonth;\n        const scansRemaining = scansLimit === -1 ? -1 : Math.max(0, scansLimit - monthlyScans);\n        const scansUsagePercentage = scansLimit === -1 ? 0 : Math.min(100, monthlyScans / scansLimit * 100);\n        // Determine if user can upgrade/downgrade\n        const canUpgrade = userWithSubscription.plan !== 'ENTERPRISE';\n        const canDowngrade = userWithSubscription.plan !== 'FREE';\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            subscription: {\n                plan: userWithSubscription.plan,\n                status: userWithSubscription.subscriptionStatus,\n                currentPeriodEnd: userWithSubscription.currentPeriodEnd,\n                cancelAtPeriodEnd: userWithSubscription.cancelAtPeriodEnd,\n                stripeCustomerId: userWithSubscription.stripeCustomerId,\n                hasActiveSubscription: userWithSubscription.subscriptionStatus === 'active'\n            },\n            usage: {\n                monthlyScans: monthlyScans,\n                scansLimit: scansLimit,\n                scansRemaining: scansRemaining,\n                scansUsagePercentage: Math.round(scansUsagePercentage)\n            },\n            planConfig: planConfig,\n            billing: {\n                canUpgrade: canUpgrade,\n                canDowngrade: canDowngrade,\n                hasPaymentMethod: !!userWithSubscription.stripeCustomerId,\n                recentPayments: userWithSubscription.payments\n            }\n        });\n    } catch (error) {\n        console.error('Subscription status error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch subscription status'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/subscription/status/route.js\n");

/***/ }),

/***/ "(rsc)/./lib/auth.js":
/*!*********************!*\
  !*** ./lib/auth.js ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cleanupExpiredSessions: () => (/* binding */ cleanupExpiredSessions),\n/* harmony export */   createSession: () => (/* binding */ createSession),\n/* harmony export */   generateToken: () => (/* binding */ generateToken),\n/* harmony export */   getUserFromToken: () => (/* binding */ getUserFromToken),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   invalidateSession: () => (/* binding */ invalidateSession),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   validatePassword: () => (/* binding */ validatePassword),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _db_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./db.js */ \"(rsc)/./lib/db.js\");\n\n\n\n// JWT Configuration\nconst JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-change-in-production';\nconst JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';\n/**\n * Hash a password using bcrypt\n * @param {string} password - Plain text password\n * @returns {Promise<string>} - Hashed password\n */ async function hashPassword(password) {\n    const saltRounds = 12;\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_0___default().hash(password, saltRounds);\n}\n/**\n * Verify a password against its hash\n * @param {string} password - Plain text password\n * @param {string} hashedPassword - Hashed password from database\n * @returns {Promise<boolean>} - True if password matches\n */ async function verifyPassword(password, hashedPassword) {\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_0___default().compare(password, hashedPassword);\n}\n/**\n * Generate a JWT token for a user\n * @param {Object} user - User object with id and email\n * @returns {string} - JWT token\n */ function generateToken(user) {\n    const payload = {\n        userId: user.id,\n        email: user.email,\n        plan: user.plan,\n        iat: Math.floor(Date.now() / 1000)\n    };\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, JWT_SECRET, {\n        expiresIn: JWT_EXPIRES_IN\n    });\n}\n/**\n * Verify and decode a JWT token\n * @param {string} token - JWT token\n * @returns {Object|null} - Decoded token payload or null if invalid\n */ function verifyToken(token) {\n    try {\n        return jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, JWT_SECRET);\n    } catch (error) {\n        console.error('Token verification failed:', error.message);\n        return null;\n    }\n}\n/**\n * Create a session in the database\n * @param {string} userId - User ID\n * @param {string} token - JWT token\n * @returns {Promise<Object>} - Created session\n */ async function createSession(userId, token) {\n    // Calculate expiration date (7 days from now)\n    const expiresAt = new Date();\n    expiresAt.setDate(expiresAt.getDate() + 7);\n    return await _db_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.create({\n        data: {\n            userId,\n            token,\n            expiresAt\n        }\n    });\n}\n/**\n * Get user from token\n * @param {string} token - JWT token\n * @returns {Promise<Object|null>} - User object or null\n */ async function getUserFromToken(token) {\n    try {\n        const decoded = verifyToken(token);\n        if (!decoded) return null;\n        // Check if session exists and is valid\n        const session = await _db_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.findUnique({\n            where: {\n                token\n            },\n            include: {\n                user: true\n            }\n        });\n        if (!session || session.expiresAt < new Date()) {\n            // Clean up expired session\n            if (session) {\n                await _db_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.delete({\n                    where: {\n                        id: session.id\n                    }\n                });\n            }\n            return null;\n        }\n        return session.user;\n    } catch (error) {\n        console.error('Error getting user from token:', error);\n        return null;\n    }\n}\n/**\n * Invalidate a session (logout)\n * @param {string} token - JWT token\n * @returns {Promise<boolean>} - True if session was deleted\n */ async function invalidateSession(token) {\n    try {\n        await _db_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.delete({\n            where: {\n                token\n            }\n        });\n        return true;\n    } catch (error) {\n        console.error('Error invalidating session:', error);\n        return false;\n    }\n}\n/**\n * Clean up expired sessions\n * @returns {Promise<number>} - Number of deleted sessions\n */ async function cleanupExpiredSessions() {\n    try {\n        const result = await _db_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.deleteMany({\n            where: {\n                expiresAt: {\n                    lt: new Date()\n                }\n            }\n        });\n        return result.count;\n    } catch (error) {\n        console.error('Error cleaning up expired sessions:', error);\n        return 0;\n    }\n}\n/**\n * Validate email format\n * @param {string} email - Email address\n * @returns {boolean} - True if email is valid\n */ function isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\n/**\n * Validate password strength\n * @param {string} password - Password\n * @returns {Object} - Validation result with isValid and errors\n */ function validatePassword(password) {\n    const errors = [];\n    if (password.length < 8) {\n        errors.push('Password must be at least 8 characters long');\n    }\n    if (!/[A-Z]/.test(password)) {\n        errors.push('Password must contain at least one uppercase letter');\n    }\n    if (!/[a-z]/.test(password)) {\n        errors.push('Password must contain at least one lowercase letter');\n    }\n    if (!/\\d/.test(password)) {\n        errors.push('Password must contain at least one number');\n    }\n    return {\n        isValid: errors.length === 0,\n        errors\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.js\n");

/***/ }),

/***/ "(rsc)/./lib/db.js":
/*!*******************!*\
  !*** ./lib/db.js ***!
  \*******************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\n// Global variable to store the Prisma client instance\nlet prisma;\n// Initialize Prisma client with proper configuration\nif (false) {} else {\n    // In development, use a global variable to prevent multiple instances\n    // during hot reloads\n    if (!global.prisma) {\n        global.prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n            log: [\n                'query',\n                'error',\n                'warn'\n            ]\n        });\n    }\n    prisma = global.prisma;\n}\n// Graceful shutdown\nprocess.on('beforeExit', async ()=>{\n    await prisma.$disconnect();\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (prisma);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvZGIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQThDO0FBRTlDLHNEQUFzRDtBQUN0RCxJQUFJQztBQUVKLHFEQUFxRDtBQUNyRCxJQUFJQyxLQUFxQyxFQUFFLEVBSzFDLE1BQU07SUFDTCxzRUFBc0U7SUFDdEUscUJBQXFCO0lBQ3JCLElBQUksQ0FBQ0UsT0FBT0gsTUFBTSxFQUFFO1FBQ2xCRyxPQUFPSCxNQUFNLEdBQUcsSUFBSUQsd0RBQVlBLENBQUM7WUFDL0JHLEtBQUs7Z0JBQUM7Z0JBQVM7Z0JBQVM7YUFBTztRQUNqQztJQUNGO0lBQ0FGLFNBQVNHLE9BQU9ILE1BQU07QUFDeEI7QUFFQSxvQkFBb0I7QUFDcEJDLFFBQVFHLEVBQUUsQ0FBQyxjQUFjO0lBQ3ZCLE1BQU1KLE9BQU9LLFdBQVc7QUFDMUI7QUFFQSxpRUFBZUwsTUFBTUEsRUFBQyIsInNvdXJjZXMiOlsiL1VzZXJzL2dpb3JnaS9Eb2N1bWVudHMvYXVnbWVudC1wcm9qZWN0cy9maW5hbmNlL2xpYi9kYi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCc7XG5cbi8vIEdsb2JhbCB2YXJpYWJsZSB0byBzdG9yZSB0aGUgUHJpc21hIGNsaWVudCBpbnN0YW5jZVxubGV0IHByaXNtYTtcblxuLy8gSW5pdGlhbGl6ZSBQcmlzbWEgY2xpZW50IHdpdGggcHJvcGVyIGNvbmZpZ3VyYXRpb25cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIC8vIEluIHByb2R1Y3Rpb24sIGNyZWF0ZSBhIG5ldyBpbnN0YW5jZVxuICBwcmlzbWEgPSBuZXcgUHJpc21hQ2xpZW50KHtcbiAgICBsb2c6IFsnZXJyb3InXSxcbiAgfSk7XG59IGVsc2Uge1xuICAvLyBJbiBkZXZlbG9wbWVudCwgdXNlIGEgZ2xvYmFsIHZhcmlhYmxlIHRvIHByZXZlbnQgbXVsdGlwbGUgaW5zdGFuY2VzXG4gIC8vIGR1cmluZyBob3QgcmVsb2Fkc1xuICBpZiAoIWdsb2JhbC5wcmlzbWEpIHtcbiAgICBnbG9iYWwucHJpc21hID0gbmV3IFByaXNtYUNsaWVudCh7XG4gICAgICBsb2c6IFsncXVlcnknLCAnZXJyb3InLCAnd2FybiddLFxuICAgIH0pO1xuICB9XG4gIHByaXNtYSA9IGdsb2JhbC5wcmlzbWE7XG59XG5cbi8vIEdyYWNlZnVsIHNodXRkb3duXG5wcm9jZXNzLm9uKCdiZWZvcmVFeGl0JywgYXN5bmMgKCkgPT4ge1xuICBhd2FpdCBwcmlzbWEuJGRpc2Nvbm5lY3QoKTtcbn0pO1xuXG5leHBvcnQgZGVmYXVsdCBwcmlzbWE7XG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwicHJpc21hIiwicHJvY2VzcyIsImxvZyIsImdsb2JhbCIsIm9uIiwiJGRpc2Nvbm5lY3QiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./lib/db.js\n");

/***/ }),

/***/ "(rsc)/./lib/stripe.js":
/*!***********************!*\
  !*** ./lib/stripe.js ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PLAN_CONFIG: () => (/* binding */ PLAN_CONFIG),\n/* harmony export */   cancelSubscription: () => (/* binding */ cancelSubscription),\n/* harmony export */   checkPlanLimit: () => (/* binding */ checkPlanLimit),\n/* harmony export */   constructWebhookEvent: () => (/* binding */ constructWebhookEvent),\n/* harmony export */   createCheckoutSession: () => (/* binding */ createCheckoutSession),\n/* harmony export */   createPortalSession: () => (/* binding */ createPortalSession),\n/* harmony export */   createStripeCustomer: () => (/* binding */ createStripeCustomer),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getPlanConfig: () => (/* binding */ getPlanConfig),\n/* harmony export */   getSubscription: () => (/* binding */ getSubscription),\n/* harmony export */   reactivateSubscription: () => (/* binding */ reactivateSubscription)\n/* harmony export */ });\n/* harmony import */ var stripe__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! stripe */ \"(rsc)/./node_modules/stripe/esm/stripe.esm.node.js\");\n\n// Initialize Stripe with secret key\nconst stripe = new stripe__WEBPACK_IMPORTED_MODULE_0__[\"default\"](process.env.STRIPE_SECRET_KEY, {\n    apiVersion: '2023-10-16'\n});\n// Plan configuration with Stripe price IDs and limits\nconst PLAN_CONFIG = {\n    FREE: {\n        name: 'Free',\n        price: 0,\n        stripePriceId: null,\n        limits: {\n            scansPerMonth: 5,\n            maxFileSize: 1024 * 1024,\n            features: [\n                'basic-analysis'\n            ]\n        },\n        features: [\n            'Up to 5 scans per month',\n            'Basic vulnerability detection',\n            'Gas optimization suggestions',\n            'Community support'\n        ]\n    },\n    PRO: {\n        name: 'Pro',\n        price: 99,\n        stripePriceId: process.env.STRIPE_PRO_PRICE_ID,\n        limits: {\n            scansPerMonth: 100,\n            maxFileSize: 5 * 1024 * 1024,\n            features: [\n                'basic-analysis',\n                'advanced-analysis',\n                'api-access',\n                'priority-support'\n            ]\n        },\n        features: [\n            'Up to 100 scans per month',\n            'Advanced vulnerability detection',\n            'Detailed security reports',\n            'API access',\n            'Priority support',\n            'Custom security rules'\n        ]\n    },\n    ENTERPRISE: {\n        name: 'Enterprise',\n        price: 499,\n        stripePriceId: process.env.STRIPE_ENTERPRISE_PRICE_ID,\n        limits: {\n            scansPerMonth: -1,\n            maxFileSize: 10 * 1024 * 1024,\n            features: [\n                'all-features',\n                'priority-support',\n                'custom-rules',\n                'dedicated-support',\n                'sla'\n            ]\n        },\n        features: [\n            'Unlimited scans',\n            'Enterprise-grade security',\n            'Custom compliance frameworks',\n            'Dedicated support team',\n            'SLA guarantee',\n            'On-premise deployment',\n            'Custom integrations'\n        ]\n    }\n};\n/**\n * Create a Stripe customer\n * @param {Object} user - User object with email and name\n * @returns {Promise<Object>} - Stripe customer object\n */ async function createStripeCustomer(user) {\n    try {\n        const customer = await stripe.customers.create({\n            email: user.email,\n            name: user.name,\n            metadata: {\n                userId: user.id,\n                plan: user.plan\n            }\n        });\n        return customer;\n    } catch (error) {\n        console.error('Error creating Stripe customer:', error);\n        throw error;\n    }\n}\n/**\n * Create a checkout session for subscription\n * @param {string} customerId - Stripe customer ID\n * @param {string} priceId - Stripe price ID\n * @param {string} successUrl - Success redirect URL\n * @param {string} cancelUrl - Cancel redirect URL\n * @returns {Promise<Object>} - Stripe checkout session\n */ async function createCheckoutSession(customerId, priceId, successUrl, cancelUrl) {\n    try {\n        const session = await stripe.checkout.sessions.create({\n            customer: customerId,\n            payment_method_types: [\n                'card'\n            ],\n            line_items: [\n                {\n                    price: priceId,\n                    quantity: 1\n                }\n            ],\n            mode: 'subscription',\n            success_url: successUrl,\n            cancel_url: cancelUrl,\n            allow_promotion_codes: true,\n            billing_address_collection: 'required',\n            metadata: {\n                customerId: customerId\n            }\n        });\n        return session;\n    } catch (error) {\n        console.error('Error creating checkout session:', error);\n        throw error;\n    }\n}\n/**\n * Create a customer portal session\n * @param {string} customerId - Stripe customer ID\n * @param {string} returnUrl - Return URL after portal session\n * @returns {Promise<Object>} - Stripe portal session\n */ async function createPortalSession(customerId, returnUrl) {\n    try {\n        const session = await stripe.billingPortal.sessions.create({\n            customer: customerId,\n            return_url: returnUrl\n        });\n        return session;\n    } catch (error) {\n        console.error('Error creating portal session:', error);\n        throw error;\n    }\n}\n/**\n * Retrieve a subscription from Stripe\n * @param {string} subscriptionId - Stripe subscription ID\n * @returns {Promise<Object>} - Stripe subscription object\n */ async function getSubscription(subscriptionId) {\n    try {\n        const subscription = await stripe.subscriptions.retrieve(subscriptionId);\n        return subscription;\n    } catch (error) {\n        console.error('Error retrieving subscription:', error);\n        throw error;\n    }\n}\n/**\n * Cancel a subscription\n * @param {string} subscriptionId - Stripe subscription ID\n * @param {boolean} atPeriodEnd - Whether to cancel at period end\n * @returns {Promise<Object>} - Updated subscription object\n */ async function cancelSubscription(subscriptionId, atPeriodEnd = true) {\n    try {\n        const subscription = await stripe.subscriptions.update(subscriptionId, {\n            cancel_at_period_end: atPeriodEnd\n        });\n        return subscription;\n    } catch (error) {\n        console.error('Error canceling subscription:', error);\n        throw error;\n    }\n}\n/**\n * Reactivate a subscription\n * @param {string} subscriptionId - Stripe subscription ID\n * @returns {Promise<Object>} - Updated subscription object\n */ async function reactivateSubscription(subscriptionId) {\n    try {\n        const subscription = await stripe.subscriptions.update(subscriptionId, {\n            cancel_at_period_end: false\n        });\n        return subscription;\n    } catch (error) {\n        console.error('Error reactivating subscription:', error);\n        throw error;\n    }\n}\n/**\n * Construct webhook event from request\n * @param {string} body - Raw request body\n * @param {string} signature - Stripe signature header\n * @returns {Object} - Stripe event object\n */ function constructWebhookEvent(body, signature) {\n    try {\n        const event = stripe.webhooks.constructEvent(body, signature, process.env.STRIPE_WEBHOOK_SECRET);\n        return event;\n    } catch (error) {\n        console.error('Error constructing webhook event:', error);\n        throw error;\n    }\n}\n/**\n * Get plan configuration by plan name\n * @param {string} planName - Plan name (FREE, PRO, ENTERPRISE)\n * @returns {Object} - Plan configuration\n */ function getPlanConfig(planName) {\n    return PLAN_CONFIG[planName] || PLAN_CONFIG.FREE;\n}\n/**\n * Check if user has exceeded plan limits\n * @param {Object} user - User object with plan and usage data\n * @param {string} limitType - Type of limit to check (scansPerMonth, maxFileSize)\n * @param {number} currentValue - Current value to check against limit\n * @returns {boolean} - Whether limit is exceeded\n */ function checkPlanLimit(user, limitType, currentValue) {\n    const planConfig = getPlanConfig(user.plan);\n    const limit = planConfig.limits[limitType];\n    // -1 means unlimited\n    if (limit === -1) return false;\n    return currentValue >= limit;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (stripe);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/stripe.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsubscription%2Fstatus%2Froute&page=%2Fapi%2Fsubscription%2Fstatus%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsubscription%2Fstatus%2Froute.js&appDir=%2FUsers%2Fgiorgi%2FDocuments%2Faugment-projects%2Ffinance%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fgiorgi%2FDocuments%2Faugment-projects%2Ffinance&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsubscription%2Fstatus%2Froute&page=%2Fapi%2Fsubscription%2Fstatus%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsubscription%2Fstatus%2Froute.js&appDir=%2FUsers%2Fgiorgi%2FDocuments%2Faugment-projects%2Ffinance%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fgiorgi%2FDocuments%2Faugment-projects%2Ffinance&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_giorgi_Documents_augment_projects_finance_app_api_subscription_status_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/subscription/status/route.js */ \"(rsc)/./app/api/subscription/status/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/subscription/status/route\",\n        pathname: \"/api/subscription/status\",\n        filename: \"route\",\n        bundlePath: \"app/api/subscription/status/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/augment-projects/finance/app/api/subscription/status/route.js\",\n    nextConfigOutput,\n    userland: _Users_giorgi_Documents_augment_projects_finance_app_api_subscription_status_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZzdWJzY3JpcHRpb24lMkZzdGF0dXMlMkZyb3V0ZSZwYWdlPSUyRmFwaSUyRnN1YnNjcmlwdGlvbiUyRnN0YXR1cyUyRnJvdXRlJmFwcFBhdGhzPSZwYWdlUGF0aD1wcml2YXRlLW5leHQtYXBwLWRpciUyRmFwaSUyRnN1YnNjcmlwdGlvbiUyRnN0YXR1cyUyRnJvdXRlLmpzJmFwcERpcj0lMkZVc2VycyUyRmdpb3JnaSUyRkRvY3VtZW50cyUyRmF1Z21lbnQtcHJvamVjdHMlMkZmaW5hbmNlJTJGYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj0lMkZVc2VycyUyRmdpb3JnaSUyRkRvY3VtZW50cyUyRmF1Z21lbnQtcHJvamVjdHMlMkZmaW5hbmNlJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUErRjtBQUN2QztBQUNxQjtBQUNxQztBQUNsSDtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IseUdBQW1CO0FBQzNDO0FBQ0EsY0FBYyxrRUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsWUFBWTtBQUNaLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxRQUFRLHNEQUFzRDtBQUM5RDtBQUNBLFdBQVcsNEVBQVc7QUFDdEI7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUMwRjs7QUFFMUYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBcHBSb3V0ZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUtbW9kdWxlcy9hcHAtcm91dGUvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBwYXRjaEZldGNoIGFzIF9wYXRjaEZldGNoIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvbGliL3BhdGNoLWZldGNoXCI7XG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiL1VzZXJzL2dpb3JnaS9Eb2N1bWVudHMvYXVnbWVudC1wcm9qZWN0cy9maW5hbmNlL2FwcC9hcGkvc3Vic2NyaXB0aW9uL3N0YXR1cy9yb3V0ZS5qc1wiO1xuLy8gV2UgaW5qZWN0IHRoZSBuZXh0Q29uZmlnT3V0cHV0IGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCBuZXh0Q29uZmlnT3V0cHV0ID0gXCJcIlxuY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUm91dGVSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1JPVVRFLFxuICAgICAgICBwYWdlOiBcIi9hcGkvc3Vic2NyaXB0aW9uL3N0YXR1cy9yb3V0ZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXBpL3N1YnNjcmlwdGlvbi9zdGF0dXNcIixcbiAgICAgICAgZmlsZW5hbWU6IFwicm91dGVcIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvYXBpL3N1YnNjcmlwdGlvbi9zdGF0dXMvcm91dGVcIlxuICAgIH0sXG4gICAgcmVzb2x2ZWRQYWdlUGF0aDogXCIvVXNlcnMvZ2lvcmdpL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL2ZpbmFuY2UvYXBwL2FwaS9zdWJzY3JpcHRpb24vc3RhdHVzL3JvdXRlLmpzXCIsXG4gICAgbmV4dENvbmZpZ091dHB1dCxcbiAgICB1c2VybGFuZFxufSk7XG4vLyBQdWxsIG91dCB0aGUgZXhwb3J0cyB0aGF0IHdlIG5lZWQgdG8gZXhwb3NlIGZyb20gdGhlIG1vZHVsZS4gVGhpcyBzaG91bGRcbi8vIGJlIGVsaW1pbmF0ZWQgd2hlbiB3ZSd2ZSBtb3ZlZCB0aGUgb3RoZXIgcm91dGVzIHRvIHRoZSBuZXcgZm9ybWF0LiBUaGVzZVxuLy8gYXJlIHVzZWQgdG8gaG9vayBpbnRvIHRoZSByb3V0ZS5cbmNvbnN0IHsgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzIH0gPSByb3V0ZU1vZHVsZTtcbmZ1bmN0aW9uIHBhdGNoRmV0Y2goKSB7XG4gICAgcmV0dXJuIF9wYXRjaEZldGNoKHtcbiAgICAgICAgd29ya0FzeW5jU3RvcmFnZSxcbiAgICAgICAgd29ya1VuaXRBc3luY1N0b3JhZ2VcbiAgICB9KTtcbn1cbmV4cG9ydCB7IHJvdXRlTW9kdWxlLCB3b3JrQXN5bmNTdG9yYWdlLCB3b3JrVW5pdEFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MsIHBhdGNoRmV0Y2gsICB9O1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcm91dGUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsubscription%2Fstatus%2Froute&page=%2Fapi%2Fsubscription%2Fstatus%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsubscription%2Fstatus%2Froute.js&appDir=%2FUsers%2Fgiorgi%2FDocuments%2Faugment-projects%2Ffinance%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fgiorgi%2FDocuments%2Faugment-projects%2Ffinance&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/stripe","vendor-chunks/next","vendor-chunks/semver","vendor-chunks/jsonwebtoken","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/qs","vendor-chunks/jws","vendor-chunks/call-bind-apply-helpers","vendor-chunks/get-proto","vendor-chunks/object-inspect","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/bcryptjs","vendor-chunks/side-channel","vendor-chunks/side-channel-weakmap","vendor-chunks/side-channel-map","vendor-chunks/side-channel-list","vendor-chunks/safe-buffer","vendor-chunks/ms","vendor-chunks/lodash.once","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isplainobject","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isinteger","vendor-chunks/lodash.isboolean","vendor-chunks/lodash.includes","vendor-chunks/jwa","vendor-chunks/hasown","vendor-chunks/get-intrinsic","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/call-bound","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsubscription%2Fstatus%2Froute&page=%2Fapi%2Fsubscription%2Fstatus%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsubscription%2Fstatus%2Froute.js&appDir=%2FUsers%2Fgiorgi%2FDocuments%2Faugment-projects%2Ffinance%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fgiorgi%2FDocuments%2Faugment-projects%2Ffinance&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();