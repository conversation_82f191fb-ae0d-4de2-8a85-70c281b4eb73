/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/login/route";
exports.ids = ["app/api/login/route"];
exports.modules = {

/***/ "(rsc)/./app/api/login/route.js":
/*!********************************!*\
  !*** ./app/api/login/route.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_db_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../lib/db.js */ \"(rsc)/./lib/db.js\");\n/* harmony import */ var _lib_auth_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../lib/auth.js */ \"(rsc)/./lib/auth.js\");\n\n\n\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { email, password } = body;\n        // Validate required fields\n        if (!email || !password) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Email and password are required'\n            }, {\n                status: 400\n            });\n        }\n        // Validate email format\n        if (!(0,_lib_auth_js__WEBPACK_IMPORTED_MODULE_2__.isValidEmail)(email)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid email format'\n            }, {\n                status: 400\n            });\n        }\n        // Find user in database\n        const user = await _lib_db_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"].user.findUnique({\n            where: {\n                email: email.toLowerCase()\n            }\n        });\n        if (!user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid email or password'\n            }, {\n                status: 401\n            });\n        }\n        // Verify password\n        const isPasswordValid = await (0,_lib_auth_js__WEBPACK_IMPORTED_MODULE_2__.verifyPassword)(password, user.password);\n        if (!isPasswordValid) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid email or password'\n            }, {\n                status: 401\n            });\n        }\n        // Check if account is active\n        if (!user.isActive) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Account is deactivated. Please contact support.'\n            }, {\n                status: 403\n            });\n        }\n        // Update last login\n        await _lib_db_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"].user.update({\n            where: {\n                id: user.id\n            },\n            data: {\n                lastLogin: new Date()\n            }\n        });\n        // Generate JWT token and create session\n        const token = (0,_lib_auth_js__WEBPACK_IMPORTED_MODULE_2__.generateToken)(user);\n        await (0,_lib_auth_js__WEBPACK_IMPORTED_MODULE_2__.createSession)(user.id, token);\n        // Log login for monitoring\n        console.log('User login:', {\n            timestamp: new Date().toISOString(),\n            userId: user.id,\n            email: user.email\n        });\n        // Return success response (don't include password)\n        const { password: _, ...userResponse } = user;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Login successful',\n            user: userResponse,\n            token,\n            dashboard: '/dashboard'\n        });\n    } catch (error) {\n        console.error('Login error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Login failed. Please try again.'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/login/route.js\n");

/***/ }),

/***/ "(rsc)/./lib/auth.js":
/*!*********************!*\
  !*** ./lib/auth.js ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cleanupExpiredSessions: () => (/* binding */ cleanupExpiredSessions),\n/* harmony export */   createSession: () => (/* binding */ createSession),\n/* harmony export */   generateToken: () => (/* binding */ generateToken),\n/* harmony export */   getUserFromToken: () => (/* binding */ getUserFromToken),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   invalidateSession: () => (/* binding */ invalidateSession),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   validatePassword: () => (/* binding */ validatePassword),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _db_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./db.js */ \"(rsc)/./lib/db.js\");\n\n\n\n// JWT Configuration\nconst JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-change-in-production';\nconst JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';\n/**\n * Hash a password using bcrypt\n * @param {string} password - Plain text password\n * @returns {Promise<string>} - Hashed password\n */ async function hashPassword(password) {\n    const saltRounds = 12;\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_0___default().hash(password, saltRounds);\n}\n/**\n * Verify a password against its hash\n * @param {string} password - Plain text password\n * @param {string} hashedPassword - Hashed password from database\n * @returns {Promise<boolean>} - True if password matches\n */ async function verifyPassword(password, hashedPassword) {\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_0___default().compare(password, hashedPassword);\n}\n/**\n * Generate a JWT token for a user\n * @param {Object} user - User object with id and email\n * @returns {string} - JWT token\n */ function generateToken(user) {\n    const payload = {\n        userId: user.id,\n        email: user.email,\n        plan: user.plan,\n        iat: Math.floor(Date.now() / 1000)\n    };\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, JWT_SECRET, {\n        expiresIn: JWT_EXPIRES_IN\n    });\n}\n/**\n * Verify and decode a JWT token\n * @param {string} token - JWT token\n * @returns {Object|null} - Decoded token payload or null if invalid\n */ function verifyToken(token) {\n    try {\n        return jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, JWT_SECRET);\n    } catch (error) {\n        console.error('Token verification failed:', error.message);\n        return null;\n    }\n}\n/**\n * Create a session in the database\n * @param {string} userId - User ID\n * @param {string} token - JWT token\n * @returns {Promise<Object>} - Created session\n */ async function createSession(userId, token) {\n    // Calculate expiration date (7 days from now)\n    const expiresAt = new Date();\n    expiresAt.setDate(expiresAt.getDate() + 7);\n    return await _db_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.create({\n        data: {\n            userId,\n            token,\n            expiresAt\n        }\n    });\n}\n/**\n * Get user from token\n * @param {string} token - JWT token\n * @returns {Promise<Object|null>} - User object or null\n */ async function getUserFromToken(token) {\n    try {\n        const decoded = verifyToken(token);\n        if (!decoded) return null;\n        // Check if session exists and is valid\n        const session = await _db_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.findUnique({\n            where: {\n                token\n            },\n            include: {\n                user: true\n            }\n        });\n        if (!session || session.expiresAt < new Date()) {\n            // Clean up expired session\n            if (session) {\n                await _db_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.delete({\n                    where: {\n                        id: session.id\n                    }\n                });\n            }\n            return null;\n        }\n        return session.user;\n    } catch (error) {\n        console.error('Error getting user from token:', error);\n        return null;\n    }\n}\n/**\n * Invalidate a session (logout)\n * @param {string} token - JWT token\n * @returns {Promise<boolean>} - True if session was deleted\n */ async function invalidateSession(token) {\n    try {\n        await _db_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.delete({\n            where: {\n                token\n            }\n        });\n        return true;\n    } catch (error) {\n        console.error('Error invalidating session:', error);\n        return false;\n    }\n}\n/**\n * Clean up expired sessions\n * @returns {Promise<number>} - Number of deleted sessions\n */ async function cleanupExpiredSessions() {\n    try {\n        const result = await _db_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.deleteMany({\n            where: {\n                expiresAt: {\n                    lt: new Date()\n                }\n            }\n        });\n        return result.count;\n    } catch (error) {\n        console.error('Error cleaning up expired sessions:', error);\n        return 0;\n    }\n}\n/**\n * Validate email format\n * @param {string} email - Email address\n * @returns {boolean} - True if email is valid\n */ function isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\n/**\n * Validate password strength\n * @param {string} password - Password\n * @returns {Object} - Validation result with isValid and errors\n */ function validatePassword(password) {\n    const errors = [];\n    if (password.length < 8) {\n        errors.push('Password must be at least 8 characters long');\n    }\n    if (!/[A-Z]/.test(password)) {\n        errors.push('Password must contain at least one uppercase letter');\n    }\n    if (!/[a-z]/.test(password)) {\n        errors.push('Password must contain at least one lowercase letter');\n    }\n    if (!/\\d/.test(password)) {\n        errors.push('Password must contain at least one number');\n    }\n    return {\n        isValid: errors.length === 0,\n        errors\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.js\n");

/***/ }),

/***/ "(rsc)/./lib/db.js":
/*!*******************!*\
  !*** ./lib/db.js ***!
  \*******************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\n// Global variable to store the Prisma client instance\nlet prisma;\n// Initialize Prisma client with proper configuration\nif (false) {} else {\n    // In development, use a global variable to prevent multiple instances\n    // during hot reloads\n    if (!global.prisma) {\n        global.prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n            log: [\n                'query',\n                'error',\n                'warn'\n            ]\n        });\n    }\n    prisma = global.prisma;\n}\n// Graceful shutdown\nprocess.on('beforeExit', async ()=>{\n    await prisma.$disconnect();\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (prisma);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvZGIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQThDO0FBRTlDLHNEQUFzRDtBQUN0RCxJQUFJQztBQUVKLHFEQUFxRDtBQUNyRCxJQUFJQyxLQUFxQyxFQUFFLEVBSzFDLE1BQU07SUFDTCxzRUFBc0U7SUFDdEUscUJBQXFCO0lBQ3JCLElBQUksQ0FBQ0UsT0FBT0gsTUFBTSxFQUFFO1FBQ2xCRyxPQUFPSCxNQUFNLEdBQUcsSUFBSUQsd0RBQVlBLENBQUM7WUFDL0JHLEtBQUs7Z0JBQUM7Z0JBQVM7Z0JBQVM7YUFBTztRQUNqQztJQUNGO0lBQ0FGLFNBQVNHLE9BQU9ILE1BQU07QUFDeEI7QUFFQSxvQkFBb0I7QUFDcEJDLFFBQVFHLEVBQUUsQ0FBQyxjQUFjO0lBQ3ZCLE1BQU1KLE9BQU9LLFdBQVc7QUFDMUI7QUFFQSxpRUFBZUwsTUFBTUEsRUFBQyIsInNvdXJjZXMiOlsiL1VzZXJzL2dpb3JnaS9Eb2N1bWVudHMvYXVnbWVudC1wcm9qZWN0cy9maW5hbmNlL2xpYi9kYi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCc7XG5cbi8vIEdsb2JhbCB2YXJpYWJsZSB0byBzdG9yZSB0aGUgUHJpc21hIGNsaWVudCBpbnN0YW5jZVxubGV0IHByaXNtYTtcblxuLy8gSW5pdGlhbGl6ZSBQcmlzbWEgY2xpZW50IHdpdGggcHJvcGVyIGNvbmZpZ3VyYXRpb25cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIC8vIEluIHByb2R1Y3Rpb24sIGNyZWF0ZSBhIG5ldyBpbnN0YW5jZVxuICBwcmlzbWEgPSBuZXcgUHJpc21hQ2xpZW50KHtcbiAgICBsb2c6IFsnZXJyb3InXSxcbiAgfSk7XG59IGVsc2Uge1xuICAvLyBJbiBkZXZlbG9wbWVudCwgdXNlIGEgZ2xvYmFsIHZhcmlhYmxlIHRvIHByZXZlbnQgbXVsdGlwbGUgaW5zdGFuY2VzXG4gIC8vIGR1cmluZyBob3QgcmVsb2Fkc1xuICBpZiAoIWdsb2JhbC5wcmlzbWEpIHtcbiAgICBnbG9iYWwucHJpc21hID0gbmV3IFByaXNtYUNsaWVudCh7XG4gICAgICBsb2c6IFsncXVlcnknLCAnZXJyb3InLCAnd2FybiddLFxuICAgIH0pO1xuICB9XG4gIHByaXNtYSA9IGdsb2JhbC5wcmlzbWE7XG59XG5cbi8vIEdyYWNlZnVsIHNodXRkb3duXG5wcm9jZXNzLm9uKCdiZWZvcmVFeGl0JywgYXN5bmMgKCkgPT4ge1xuICBhd2FpdCBwcmlzbWEuJGRpc2Nvbm5lY3QoKTtcbn0pO1xuXG5leHBvcnQgZGVmYXVsdCBwcmlzbWE7XG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwicHJpc21hIiwicHJvY2VzcyIsImxvZyIsImdsb2JhbCIsIm9uIiwiJGRpc2Nvbm5lY3QiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./lib/db.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Flogin%2Froute&page=%2Fapi%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Flogin%2Froute.js&appDir=%2FUsers%2Fgiorgi%2FDocuments%2Faugment-projects%2Ffinance%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fgiorgi%2FDocuments%2Faugment-projects%2Ffinance&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Flogin%2Froute&page=%2Fapi%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Flogin%2Froute.js&appDir=%2FUsers%2Fgiorgi%2FDocuments%2Faugment-projects%2Ffinance%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fgiorgi%2FDocuments%2Faugment-projects%2Ffinance&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_giorgi_Documents_augment_projects_finance_app_api_login_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/login/route.js */ \"(rsc)/./app/api/login/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/login/route\",\n        pathname: \"/api/login\",\n        filename: \"route\",\n        bundlePath: \"app/api/login/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/augment-projects/finance/app/api/login/route.js\",\n    nextConfigOutput,\n    userland: _Users_giorgi_Documents_augment_projects_finance_app_api_login_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Flogin%2Froute&page=%2Fapi%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Flogin%2Froute.js&appDir=%2FUsers%2Fgiorgi%2FDocuments%2Faugment-projects%2Ffinance%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fgiorgi%2FDocuments%2Faugment-projects%2Ffinance&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Flogin%2Froute&page=%2Fapi%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Flogin%2Froute.js&appDir=%2FUsers%2Fgiorgi%2FDocuments%2Faugment-projects%2Ffinance%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fgiorgi%2FDocuments%2Faugment-projects%2Ffinance&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();