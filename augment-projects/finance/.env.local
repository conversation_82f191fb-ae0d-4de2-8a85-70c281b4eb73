# Database Configuration
# For development, using SQLite for simplicity
# In production, use PostgreSQL: postgresql://username:password@localhost:5432/secureaudit_db?schema=public
DATABASE_URL="file:./dev.db"

# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_EXPIRES_IN="7d"

# Email Configuration (existing)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="demo-password"
SMTP_FROM="<EMAIL>"

# Application Configuration
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-nextauth-secret-change-this-in-production"

# Development Settings
NODE_ENV="development"
