'use client';

import { useState } from 'react';
import { loadStripe } from '@stripe/stripe-js';

// Initialize Stripe
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY);

export default function PricingCard({ plan, config, isPopular = false, userPlan = null, isAuthenticated = false }) {
  const [isLoading, setIsLoading] = useState(false);

  const handleUpgrade = async () => {
    if (!isAuthenticated) {
      // Redirect to register page
      window.location.href = '/register';
      return;
    }

    if (plan === 'FREE') {
      // Redirect to register or dashboard
      window.location.href = '/dashboard';
      return;
    }

    if (plan === 'ENTERPRISE') {
      // Redirect to contact page
      window.location.href = '/contact';
      return;
    }

    setIsLoading(true);

    try {
      // Get auth token
      const token = localStorage.getItem('auth-token');
      if (!token) {
        window.location.href = '/login';
        return;
      }

      // Create checkout session
      const response = await fetch('/api/stripe/create-checkout-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ plan })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create checkout session');
      }

      // Redirect to Stripe Checkout
      const stripe = await stripePromise;
      const { error } = await stripe.redirectToCheckout({
        sessionId: data.sessionId
      });

      if (error) {
        throw new Error(error.message);
      }

    } catch (error) {
      console.error('Upgrade error:', error);
      alert(error.message || 'Failed to start upgrade process');
    } finally {
      setIsLoading(false);
    }
  };

  const getButtonText = () => {
    if (isLoading) return 'Loading...';
    
    if (!isAuthenticated) {
      return plan === 'FREE' ? 'Start Free' : 'Sign Up to Upgrade';
    }

    if (userPlan === plan) {
      return 'Current Plan';
    }

    switch (plan) {
      case 'FREE':
        return 'Current Plan';
      case 'PRO':
        return userPlan === 'FREE' ? 'Upgrade to Pro' : 'Switch to Pro';
      case 'ENTERPRISE':
        return 'Contact Sales';
      default:
        return 'Select Plan';
    }
  };

  const getButtonStyle = () => {
    const baseStyle = "w-full py-3 rounded-lg font-semibold transition-all disabled:opacity-50 disabled:cursor-not-allowed";
    
    if (userPlan === plan) {
      return `${baseStyle} bg-gray-600 text-gray-300 cursor-not-allowed`;
    }

    if (isPopular) {
      return `${baseStyle} bg-gradient-to-r from-purple-600 to-blue-600 text-white hover:from-purple-700 hover:to-blue-700`;
    }

    switch (plan) {
      case 'FREE':
        return `${baseStyle} bg-blue-600 text-white hover:bg-blue-700`;
      case 'ENTERPRISE':
        return `${baseStyle} bg-green-600 text-white hover:bg-green-700`;
      default:
        return `${baseStyle} bg-blue-600 text-white hover:bg-blue-700`;
    }
  };

  const cardStyle = isPopular 
    ? "bg-gradient-to-b from-purple-600/20 to-blue-600/20 backdrop-blur-sm p-8 rounded-xl border border-purple-500 relative transform scale-105"
    : "bg-slate-800/50 backdrop-blur-sm p-8 rounded-xl border border-slate-700 hover:border-blue-500 transition-all";

  return (
    <div className={cardStyle}>
      {isPopular && (
        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
          <span className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-4 py-1 rounded-full text-sm font-semibold">
            Most Popular
          </span>
        </div>
      )}
      
      <div className="text-center">
        <h3 className="text-2xl font-bold text-white mb-4">{config.name}</h3>
        
        <div className="mb-2">
          {plan === 'ENTERPRISE' ? (
            <div className="text-4xl font-bold text-green-400">Custom</div>
          ) : plan === 'FREE' ? (
            <div className="text-4xl font-bold text-blue-400">$0</div>
          ) : (
            <div className="text-4xl font-bold text-purple-400">${config.price}</div>
          )}
        </div>
        
        <p className="text-gray-400 mb-6">
          {plan === 'FREE' ? 'Perfect for testing' : 
           plan === 'ENTERPRISE' ? 'for large teams' : 'per month'}
        </p>

        <ul className="text-left space-y-3 mb-8">
          {config.features.map((feature, index) => (
            <li key={index} className="flex items-center text-gray-300">
              <span className="text-green-400 mr-2">✓</span>
              {feature}
            </li>
          ))}
        </ul>

        <button
          onClick={handleUpgrade}
          disabled={isLoading || (userPlan === plan)}
          className={getButtonStyle()}
        >
          {getButtonText()}
        </button>

        {userPlan === plan && (
          <p className="text-sm text-gray-400 mt-2">You're currently on this plan</p>
        )}
      </div>
    </div>
  );
}
