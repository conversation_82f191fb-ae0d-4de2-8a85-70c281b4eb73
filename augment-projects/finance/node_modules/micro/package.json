{"name": "micro", "version": "10.0.1", "description": "Asynchronous HTTP microservices", "license": "MIT", "main": "./dist/src/lib/index.js", "types": "./types/src/lib", "files": ["src", "dist", "types"], "bin": {"micro": "./dist/src/bin/micro.js"}, "engines": {"node": ">= 16.0.0"}, "scripts": {"build": "tsc", "prepublishOnly": "yarn run build", "eslint-check": "eslint --max-warnings=0 .", "prettier-check": "prettier --check .", "type-check": "tsc --noEmit"}, "repository": "vercel/micro", "keywords": ["micro", "service", "microservice", "serverless", "API"], "dependencies": {"arg": "4.1.0", "content-type": "1.0.4", "raw-body": "2.4.1"}, "devDependencies": {"@types/content-type": "1.1.5", "@types/node": "18.0.3", "@vercel/style-guide": "3.0.0", "eslint": "8.19.0", "prettier": "2.7.1", "typescript": "4.7.4"}, "prettier": "@vercel/style-guide/prettier"}