// SecureAudit Database Schema
// Comprehensive schema for user management, scan results, and analytics

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// User model for authentication and profile management
model User {
  id         String    @id @default(cuid())
  email      String    @unique
  password   String
  name       String
  company    String?
  plan       Plan      @default(FREE)
  isActive   Boolean   @default(true)
  isVerified Boolean   @default(false)
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt
  lastLogin  DateTime?

  // Subscription fields
  stripeCustomerId   String?   @unique
  subscriptionStatus String    @default("inactive") // active, inactive, past_due, canceled, trialing
  currentPeriodEnd   DateTime?
  cancelAtPeriodEnd  Boolean   @default(false)

  // Usage tracking
  monthlyScans  Int      @default(0)
  lastScanReset DateTime @default(now())

  // Relationships
  scans        Scan[]
  sessions     Session[]
  subscription Subscription?
  payments     Payment[]

  @@map("users")
}

// Scan results model for storing smart contract analysis
model Scan {
  id          String  @id @default(cuid())
  userId      String
  fileName    String
  fileSize    Int
  fileContent String? @db.Text // Optional: store file content for re-analysis
  analysisId  String  @unique

  // Analysis Results
  securityScore    Int
  riskLevel        RiskLevel
  vulnerabilities  Json // Store vulnerability details as JSON
  gasOptimizations Json // Store gas optimization suggestions as JSON
  complianceChecks Json // Store compliance check results as JSON
  codeMetrics      Json // Store code metrics as JSON

  // Metadata
  analysisTimestamp DateTime @default(now())
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relationships
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("scans")
}

// Session model for JWT token management
model Session {
  id        String   @id @default(cuid())
  userId    String
  token     String   @db.VarChar(500)
  expiresAt DateTime
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relationships
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

// Subscription model for Stripe subscription management
model Subscription {
  id                   String             @id @default(cuid())
  userId               String             @unique
  stripeSubscriptionId String             @unique
  stripePriceId        String
  stripeProductId      String
  status               SubscriptionStatus @default(INACTIVE)
  currentPeriodStart   DateTime
  currentPeriodEnd     DateTime
  cancelAtPeriodEnd    Boolean            @default(false)
  canceledAt           DateTime?
  trialStart           DateTime?
  trialEnd             DateTime?
  createdAt            DateTime           @default(now())
  updatedAt            DateTime           @updatedAt

  // Relationships
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("subscriptions")
}

// Payment model for tracking payment history
model Payment {
  id              String        @id @default(cuid())
  userId          String
  stripePaymentId String        @unique
  stripeInvoiceId String?
  amount          Int // Amount in cents
  currency        String        @default("usd")
  status          PaymentStatus @default(PENDING)
  description     String?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  // Relationships
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("payments")
}

// Enums for MySQL
enum Plan {
  FREE
  PRO
  ENTERPRISE
}

enum RiskLevel {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum SubscriptionStatus {
  ACTIVE
  INACTIVE
  PAST_DUE
  CANCELED
  TRIALING
  INCOMPLETE
  INCOMPLETE_EXPIRED
  UNPAID
}

enum PaymentStatus {
  PENDING
  SUCCEEDED
  FAILED
  CANCELED
  REFUNDED
}
