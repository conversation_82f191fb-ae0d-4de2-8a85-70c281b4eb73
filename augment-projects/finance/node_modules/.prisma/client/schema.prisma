// SecureAudit Database Schema
// Comprehensive schema for user management, scan results, and analytics

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// User model for authentication and profile management
model User {
  id         String    @id @default(cuid())
  email      String    @unique
  password   String
  name       String
  company    String?
  plan       Plan      @default(FREE)
  isActive   Boolean   @default(true)
  isVerified Boolean   @default(false)
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt
  lastLogin  DateTime?

  // Relationships
  scans    Scan[]
  sessions Session[]

  @@map("users")
}

// Scan results model for storing smart contract analysis
model Scan {
  id          String  @id @default(cuid())
  userId      String
  fileName    String
  fileSize    Int
  fileContent String? @db.Text // Optional: store file content for re-analysis
  analysisId  String  @unique

  // Analysis Results
  securityScore    Int
  riskLevel        RiskLevel
  vulnerabilities  Json // Store vulnerability details as JSON
  gasOptimizations Json // Store gas optimization suggestions as JSON
  complianceChe<PERSON> <PERSON>son // Store compliance check results as JSON
  codeMetrics      Json // Store code metrics as JSON

  // Metadata
  analysisTimestamp DateTime @default(now())
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relationships
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("scans")
}

// Session model for JWT token management
model Session {
  id        String   @id @default(cuid())
  userId    String
  token     String   @db.VarChar(500)
  expiresAt DateTime
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relationships
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

// Enums for MySQL
enum Plan {
  FREE
  PRO
  ENTERPRISE
}

enum RiskLevel {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}
