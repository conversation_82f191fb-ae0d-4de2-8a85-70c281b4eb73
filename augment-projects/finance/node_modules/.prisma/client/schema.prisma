// SecureAudit Database Schema
// Comprehensive schema for user management, scan results, and analytics

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// User model for authentication and profile management
model User {
  id         String    @id @default(cuid())
  email      String    @unique
  password   String
  name       String
  company    String?
  plan       String    @default("FREE")
  isActive   Boolean   @default(true)
  isVerified Boolean   @default(false)
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt
  lastLogin  DateTime?

  // Relationships
  scans    Scan[]
  sessions Session[]

  @@map("users")
}

// Scan results model for storing smart contract analysis
model Scan {
  id          String  @id @default(cuid())
  userId      String
  fileName    String
  fileSize    Int
  fileContent String? // Optional: store file content for re-analysis
  analysisId  String  @unique

  // Analysis Results
  securityScore    Int
  riskLevel        String
  vulnerabilities  String // Store vulnerability details as JSON string
  gasOptimizations String // Store gas optimization suggestions as JSON string
  complianceChecks String // Store compliance check results as JSON string
  codeMetrics      String // Store code metrics as JSON string

  // Metadata
  analysisTimestamp DateTime @default(now())
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relationships
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("scans")
}

// Session model for JWT token management
model Session {
  id        String   @id @default(cuid())
  userId    String
  token     String   @unique
  expiresAt DateTime
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relationships
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

// Note: SQLite doesn't support enums, so we use String fields with validation in the application
// Valid Plan values: "FREE", "PRO", "ENTERPRISE"
// Valid RiskLevel values: "LOW", "MEDIUM", "HIGH", "CRITICAL"
