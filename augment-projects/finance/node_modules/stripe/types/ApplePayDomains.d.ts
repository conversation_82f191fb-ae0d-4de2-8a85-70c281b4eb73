// File generated from our OpenAPI spec

declare module 'stripe' {
  namespace Stripe {
    /**
     * The ApplePayDomain object.
     */
    interface ApplePayDomain {
      /**
       * Unique identifier for the object.
       */
      id: string;

      /**
       * String representing the object's type. Objects of the same type share the same value.
       */
      object: 'apple_pay_domain';

      /**
       * Time at which the object was created. Measured in seconds since the Unix epoch.
       */
      created: number;

      /**
       * Always true for a deleted object
       */
      deleted?: void;

      domain_name: string;

      /**
       * Has the value `true` if the object exists in live mode or the value `false` if the object exists in test mode.
       */
      livemode: boolean;
    }

    /**
     * The DeletedApplePayDomain object.
     */
    interface DeletedApplePayDomain {
      /**
       * Unique identifier for the object.
       */
      id: string;

      /**
       * String representing the object's type. Objects of the same type share the same value.
       */
      object: 'apple_pay_domain';

      /**
       * Always true for a deleted object
       */
      deleted: true;
    }
  }
}
