'use client';

import { useState, useEffect } from 'react';
import PricingCard from '../../components/PricingCard.js';
import { PLAN_CONFIG } from '../../lib/stripe.js';

export default function Pricing() {
  const [user, setUser] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const token = localStorage.getItem('auth-token');
      if (!token) {
        setIsLoading(false);
        return;
      }

      const response = await fetch('/api/user/profile', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setUser(data.user);
        setIsAuthenticated(true);
      }
    } catch (error) {
      console.error('Error checking auth status:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Navigation */}
      <nav className="container mx-auto px-6 py-6">
        <div className="flex items-center justify-between">
          <a href="/" className="flex items-center space-x-2">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-xl">🛡️</span>
            </div>
            <span className="text-white font-bold text-2xl">SecureAudit</span>
          </a>
          <div className="flex items-center space-x-4">
            {isAuthenticated ? (
              <>
                <a href="/dashboard" className="text-gray-300 hover:text-white transition-colors">
                  Dashboard
                </a>
                <span className="text-gray-400">Welcome, {user?.name}</span>
              </>
            ) : (
              <>
                <a href="/login" className="text-gray-300 hover:text-white transition-colors">
                  Sign In
                </a>
                <a href="/register" className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all">
                  Sign Up
                </a>
              </>
            )}
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="container mx-auto px-6 py-12 text-center">
        <h1 className="text-5xl font-bold text-white mb-6">
          Simple, Transparent Pricing
        </h1>
        <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
          Choose the perfect plan for your smart contract security needs. 
          Start free and upgrade as you grow.
        </p>
        
        {isAuthenticated && user && (
          <div className="bg-blue-600/20 border border-blue-500/50 rounded-lg p-4 max-w-md mx-auto mb-8">
            <p className="text-blue-300">
              Current Plan: <span className="font-bold text-white">{user.plan}</span>
            </p>
            {user.subscriptionStatus === 'active' && user.currentPeriodEnd && (
              <p className="text-sm text-blue-200 mt-1">
                Renews on {new Date(user.currentPeriodEnd).toLocaleDateString()}
              </p>
            )}
          </div>
        )}
      </section>

      {/* Pricing Cards */}
      <section className="container mx-auto px-6 py-12">
        <div className="max-w-6xl mx-auto">
          <div className="grid md:grid-cols-3 gap-8">
            <PricingCard
              plan="FREE"
              config={PLAN_CONFIG.FREE}
              userPlan={user?.plan}
              isAuthenticated={isAuthenticated}
            />
            <PricingCard
              plan="PRO"
              config={PLAN_CONFIG.PRO}
              isPopular={true}
              userPlan={user?.plan}
              isAuthenticated={isAuthenticated}
            />
            <PricingCard
              plan="ENTERPRISE"
              config={PLAN_CONFIG.ENTERPRISE}
              userPlan={user?.plan}
              isAuthenticated={isAuthenticated}
            />
          </div>
        </div>
      </section>

      {/* Features Comparison */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl font-bold text-white text-center mb-12">
            Compare Plans
          </h2>
          
          <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-slate-700">
                    <th className="text-left p-6 text-white font-semibold">Features</th>
                    <th className="text-center p-6 text-white font-semibold">Free</th>
                    <th className="text-center p-6 text-white font-semibold bg-purple-600/20">Pro</th>
                    <th className="text-center p-6 text-white font-semibold">Enterprise</th>
                  </tr>
                </thead>
                <tbody className="text-gray-300">
                  <tr className="border-b border-slate-700">
                    <td className="p-6">Monthly Scans</td>
                    <td className="text-center p-6">5</td>
                    <td className="text-center p-6 bg-purple-600/10">100</td>
                    <td className="text-center p-6">Unlimited</td>
                  </tr>
                  <tr className="border-b border-slate-700">
                    <td className="p-6">Max File Size</td>
                    <td className="text-center p-6">1MB</td>
                    <td className="text-center p-6 bg-purple-600/10">5MB</td>
                    <td className="text-center p-6">10MB</td>
                  </tr>
                  <tr className="border-b border-slate-700">
                    <td className="p-6">Basic Analysis</td>
                    <td className="text-center p-6 text-green-400">✓</td>
                    <td className="text-center p-6 bg-purple-600/10 text-green-400">✓</td>
                    <td className="text-center p-6 text-green-400">✓</td>
                  </tr>
                  <tr className="border-b border-slate-700">
                    <td className="p-6">Advanced Analysis</td>
                    <td className="text-center p-6 text-red-400">✗</td>
                    <td className="text-center p-6 bg-purple-600/10 text-green-400">✓</td>
                    <td className="text-center p-6 text-green-400">✓</td>
                  </tr>
                  <tr className="border-b border-slate-700">
                    <td className="p-6">API Access</td>
                    <td className="text-center p-6 text-red-400">✗</td>
                    <td className="text-center p-6 bg-purple-600/10 text-green-400">✓</td>
                    <td className="text-center p-6 text-green-400">✓</td>
                  </tr>
                  <tr className="border-b border-slate-700">
                    <td className="p-6">Priority Support</td>
                    <td className="text-center p-6 text-red-400">✗</td>
                    <td className="text-center p-6 bg-purple-600/10 text-green-400">✓</td>
                    <td className="text-center p-6 text-green-400">✓</td>
                  </tr>
                  <tr className="border-b border-slate-700">
                    <td className="p-6">Custom Rules</td>
                    <td className="text-center p-6 text-red-400">✗</td>
                    <td className="text-center p-6 bg-purple-600/10 text-green-400">✓</td>
                    <td className="text-center p-6 text-green-400">✓</td>
                  </tr>
                  <tr>
                    <td className="p-6">SLA & Dedicated Support</td>
                    <td className="text-center p-6 text-red-400">✗</td>
                    <td className="text-center p-6 bg-purple-600/10 text-red-400">✗</td>
                    <td className="text-center p-6 text-green-400">✓</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-white text-center mb-12">
            Frequently Asked Questions
          </h2>
          
          <div className="space-y-4">
            {[
              {
                question: "Can I change my plan anytime?",
                answer: "Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately for upgrades, and at the end of your billing cycle for downgrades."
              },
              {
                question: "What payment methods do you accept?",
                answer: "We accept all major credit cards (Visa, MasterCard, American Express) and bank transfers for Enterprise customers. All payments are processed securely through Stripe."
              },
              {
                question: "Is there a free trial for paid plans?",
                answer: "Yes, Pro plans come with a 14-day free trial. No credit card required to start. Enterprise customers can request a custom trial period."
              },
              {
                question: "What happens if I exceed my plan limits?",
                answer: "If you exceed your monthly scan limit, you'll be prompted to upgrade. We'll never charge you unexpectedly - you'll always have the option to upgrade or wait until next month."
              },
              {
                question: "Do you offer refunds?",
                answer: "Yes, we offer a 30-day money-back guarantee for all paid plans. If you're not satisfied, contact our support team for a full refund."
              }
            ].map((faq, index) => (
              <div key={index} className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700 p-6">
                <h3 className="text-lg font-semibold text-white mb-3">{faq.question}</h3>
                <p className="text-gray-300">{faq.answer}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-4xl mx-auto text-center bg-gradient-to-r from-blue-600/20 to-purple-600/20 backdrop-blur-sm p-12 rounded-2xl border border-slate-700">
          <h2 className="text-4xl font-bold text-white mb-6">
            Ready to Secure Your Smart Contracts?
          </h2>
          <p className="text-xl text-gray-300 mb-8">
            Start with our free plan and upgrade as your needs grow.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href={isAuthenticated ? "/dashboard" : "/register"}
              className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 rounded-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition-all"
            >
              {isAuthenticated ? "Go to Dashboard" : "Start Free"}
            </a>
            <a
              href="/contact"
              className="border border-gray-400 text-gray-300 px-8 py-3 rounded-lg hover:bg-white hover:text-gray-900 transition-all"
            >
              Contact Sales
            </a>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="container mx-auto px-6 py-12 border-t border-slate-700">
        <div className="flex flex-col md:flex-row justify-between items-center">
          <div className="flex items-center space-x-2 mb-4 md:mb-0">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-lg">🛡️</span>
            </div>
            <span className="text-white font-bold text-xl">SecureAudit</span>
          </div>
          <div className="flex space-x-6 text-gray-400">
            <a href="/privacy" className="hover:text-white transition-colors">Privacy</a>
            <a href="/terms" className="hover:text-white transition-colors">Terms</a>
            <a href="/contact" className="hover:text-white transition-colors">Contact</a>
            <a href="/about" className="hover:text-white transition-colors">About</a>
          </div>
        </div>
        <div className="text-center text-gray-400 mt-8">
          © 2024 SecureAudit. All rights reserved.
        </div>
      </footer>
    </div>
  );
}
