'use client';

import { useState } from 'react';

export default function Contact() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: '',
    subject: '',
    message: '',
    inquiryType: 'general'
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState(null);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (response.ok) {
        setSubmitStatus('success');
        setFormData({
          name: '',
          email: '',
          company: '',
          subject: '',
          message: '',
          inquiryType: 'general'
        });
      } else {
        setSubmitStatus('error');
        console.error('Form submission error:', result.error);
      }
    } catch (error) {
      setSubmitStatus('error');
      console.error('Form submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Navigation */}
      <nav className="container mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          <a href="/" className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-lg">🛡️</span>
            </div>
            <span className="text-white font-bold text-xl">SecureAudit</span>
          </a>
          <a href="/" className="text-gray-300 hover:text-white transition-colors">
            ← Back to Home
          </a>
        </div>
      </nav>

      {/* Hero Section */}
      <div className="container mx-auto px-6 py-20">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">Get in Touch</h1>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Have questions about smart contract security? Need enterprise support? Our team of security experts is here to help.
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <div className="bg-slate-800/50 backdrop-blur-sm p-8 rounded-xl border border-slate-700">
              <h2 className="text-2xl font-bold text-white mb-6">Send us a Message</h2>
              
              {submitStatus === 'success' && (
                <div className="bg-green-900/20 border border-green-500/30 p-4 rounded-lg mb-6">
                  <p className="text-green-400">✓ Message sent successfully! We'll get back to you within 24 hours.</p>
                </div>
              )}

              {submitStatus === 'error' && (
                <div className="bg-red-900/20 border border-red-500/30 p-4 rounded-lg mb-6">
                  <p className="text-red-400">✗ Failed to send message. Please try again or contact us directly.</p>
                </div>
              )}

              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-white mb-2">Name *</label>
                    <input
                      type="text"
                      required
                      value={formData.name}
                      onChange={(e) => setFormData({...formData, name: e.target.value})}
                      className="w-full bg-slate-700 text-white px-4 py-3 rounded-lg border border-slate-600 focus:border-blue-500 focus:outline-none"
                      placeholder="Your full name"
                    />
                  </div>
                  <div>
                    <label className="block text-white mb-2">Email *</label>
                    <input
                      type="email"
                      required
                      value={formData.email}
                      onChange={(e) => setFormData({...formData, email: e.target.value})}
                      className="w-full bg-slate-700 text-white px-4 py-3 rounded-lg border border-slate-600 focus:border-blue-500 focus:outline-none"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-white mb-2">Company</label>
                    <input
                      type="text"
                      value={formData.company}
                      onChange={(e) => setFormData({...formData, company: e.target.value})}
                      className="w-full bg-slate-700 text-white px-4 py-3 rounded-lg border border-slate-600 focus:border-blue-500 focus:outline-none"
                      placeholder="Your company name"
                    />
                  </div>
                  <div>
                    <label className="block text-white mb-2">Inquiry Type</label>
                    <select
                      value={formData.inquiryType}
                      onChange={(e) => setFormData({...formData, inquiryType: e.target.value})}
                      className="w-full bg-slate-700 text-white px-4 py-3 rounded-lg border border-slate-600 focus:border-blue-500 focus:outline-none"
                    >
                      <option value="general">General Inquiry</option>
                      <option value="sales">Sales & Pricing</option>
                      <option value="support">Technical Support</option>
                      <option value="enterprise">Enterprise Solutions</option>
                      <option value="partnership">Partnership</option>
                      <option value="security">Security Issue</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-white mb-2">Subject *</label>
                  <input
                    type="text"
                    required
                    value={formData.subject}
                    onChange={(e) => setFormData({...formData, subject: e.target.value})}
                    className="w-full bg-slate-700 text-white px-4 py-3 rounded-lg border border-slate-600 focus:border-blue-500 focus:outline-none"
                    placeholder="Brief description of your inquiry"
                  />
                </div>

                <div>
                  <label className="block text-white mb-2">Message *</label>
                  <textarea
                    rows="6"
                    required
                    value={formData.message}
                    onChange={(e) => setFormData({...formData, message: e.target.value})}
                    className="w-full bg-slate-700 text-white px-4 py-3 rounded-lg border border-slate-600 focus:border-blue-500 focus:outline-none"
                    placeholder="Tell us more about your needs, project details, or questions..."
                  ></textarea>
                </div>

                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-4 rounded-lg text-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition-all disabled:opacity-50"
                >
                  {isSubmitting ? 'Sending...' : 'Send Message'}
                </button>
              </form>
            </div>

            {/* Contact Information */}
            <div className="space-y-8">
              {/* Direct Contact */}
              <div className="bg-slate-800/50 backdrop-blur-sm p-8 rounded-xl border border-slate-700">
                <h3 className="text-xl font-bold text-white mb-6 flex items-center">
                  <span className="text-2xl mr-3">📞</span>
                  Direct Contact
                </h3>
                <div className="space-y-4">
                  <div>
                    <div className="text-blue-400 font-semibold">Sales & General Inquiries</div>
                    <div className="text-gray-300"><EMAIL></div>
                    <div className="text-gray-300">+1 (555) 123-4567</div>
                  </div>
                  <div>
                    <div className="text-green-400 font-semibold">Technical Support</div>
                    <div className="text-gray-300"><EMAIL></div>
                    <div className="text-gray-300">24/7 for Pro & Enterprise customers</div>
                  </div>
                  <div>
                    <div className="text-purple-400 font-semibold">Security Issues</div>
                    <div className="text-gray-300"><EMAIL></div>
                    <div className="text-gray-300">Urgent security matters only</div>
                  </div>
                </div>
              </div>

              {/* Office Information */}
              <div className="bg-slate-800/50 backdrop-blur-sm p-8 rounded-xl border border-slate-700">
                <h3 className="text-xl font-bold text-white mb-6 flex items-center">
                  <span className="text-2xl mr-3">🏢</span>
                  Office Locations
                </h3>
                <div className="space-y-6">
                  <div>
                    <div className="text-blue-400 font-semibold mb-2">Headquarters - San Francisco</div>
                    <div className="text-gray-300">
                      123 Security Boulevard<br/>
                      San Francisco, CA 94105<br/>
                      United States
                    </div>
                  </div>
                  <div>
                    <div className="text-purple-400 font-semibold mb-2">European Office - London</div>
                    <div className="text-gray-300">
                      45 Blockchain Street<br/>
                      London EC2A 4DP<br/>
                      United Kingdom
                    </div>
                  </div>
                </div>
              </div>

              {/* Response Times */}
              <div className="bg-slate-800/50 backdrop-blur-sm p-8 rounded-xl border border-slate-700">
                <h3 className="text-xl font-bold text-white mb-6 flex items-center">
                  <span className="text-2xl mr-3">⏱️</span>
                  Response Times
                </h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-300">General Inquiries</span>
                    <span className="text-blue-400 font-semibold">24 hours</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-300">Sales Questions</span>
                    <span className="text-green-400 font-semibold">4 hours</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-300">Technical Support</span>
                    <span className="text-purple-400 font-semibold">2 hours</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-300">Security Issues</span>
                    <span className="text-red-400 font-semibold">1 hour</span>
                  </div>
                </div>
              </div>

              {/* Social Links */}
              <div className="bg-slate-800/50 backdrop-blur-sm p-8 rounded-xl border border-slate-700">
                <h3 className="text-xl font-bold text-white mb-6 flex items-center">
                  <span className="text-2xl mr-3">🌐</span>
                  Follow Us
                </h3>
                <div className="grid grid-cols-2 gap-4">
                  <a href="#" className="flex items-center space-x-2 text-gray-300 hover:text-blue-400 transition-colors">
                    <span>📘</span>
                    <span>LinkedIn</span>
                  </a>
                  <a href="#" className="flex items-center space-x-2 text-gray-300 hover:text-blue-400 transition-colors">
                    <span>🐦</span>
                    <span>Twitter</span>
                  </a>
                  <a href="#" className="flex items-center space-x-2 text-gray-300 hover:text-purple-400 transition-colors">
                    <span>📱</span>
                    <span>GitHub</span>
                  </a>
                  <a href="#" className="flex items-center space-x-2 text-gray-300 hover:text-red-400 transition-colors">
                    <span>📺</span>
                    <span>YouTube</span>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="container mx-auto px-6 py-12 border-t border-slate-700">
        <div className="text-center text-gray-400">
          <p>© 2024 SecureAudit. All rights reserved.</p>
          <div className="mt-4 space-x-6">
            <a href="/privacy" className="hover:text-white transition-colors">Privacy Policy</a>
            <a href="/terms" className="hover:text-white transition-colors">Terms of Service</a>
            <a href="/contact" className="hover:text-white transition-colors">Contact</a>
          </div>
        </div>
      </footer>
    </div>
  );
}
