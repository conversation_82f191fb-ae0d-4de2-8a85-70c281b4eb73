'use client';

import { useState } from 'react';

export default function Home() {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadResult, setUploadResult] = useState(null);
  const [showDemo, setShowDemo] = useState(false);
  const [openFaq, setOpenFaq] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: '',
    message: ''
  });

  const handleFileUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    setIsUploading(true);
    setUploadResult(null);

    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch('/api/analyze', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (response.ok) {
        setUploadResult({
          fileName: result.fileName,
          vulnerabilities: result.vulnerabilities.length,
          riskLevel: result.riskLevel,
          gasOptimization: result.gasOptimization,
          score: result.securityScore,
          analysisId: result.analysisId,
          detailedResults: result
        });
      } else {
        alert(`Analysis failed: ${result.error}`);
      }
    } catch (error) {
      alert('Failed to analyze file. Please try again.');
      console.error('File upload error:', error);
    } finally {
      setIsUploading(false);
    }
  };

  const handleFormSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          inquiryType: 'general'
        }),
      });

      const result = await response.json();

      if (response.ok) {
        alert(`Thank you! ${result.message} We'll respond within ${result.responseTime}.`);
        setFormData({ name: '', email: '', company: '', message: '' });
      } else {
        alert(`Error: ${result.error}`);
      }
    } catch (error) {
      alert('Failed to send message. Please try again.');
      console.error('Form submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const scrollToSection = (sectionId) => {
    document.getElementById(sectionId)?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Navigation */}
      <nav className="container mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-lg">🛡️</span>
            </div>
            <span className="text-white font-bold text-xl">SecureAudit</span>
          </div>
          <div className="hidden md:flex space-x-8">
            <button onClick={() => scrollToSection('features')} className="text-gray-300 hover:text-white transition-colors">Features</button>
            <button onClick={() => scrollToSection('pricing')} className="text-gray-300 hover:text-white transition-colors">Pricing</button>
            <button onClick={() => scrollToSection('demo')} className="text-gray-300 hover:text-white transition-colors">Demo</button>
            <button onClick={() => scrollToSection('faq')} className="text-gray-300 hover:text-white transition-colors">FAQ</button>
          </div>
          <a href="/dashboard" className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-2 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all">
            Dashboard
          </a>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="container mx-auto px-6 py-20 text-center">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-5xl md:text-7xl font-bold text-white mb-6 leading-tight">
            🛡️ Instantly Secure Your{" "}
            <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
              Smart Contracts
            </span>{" "}
            with AI-Powered Audits
          </h1>
          <p className="text-xl md:text-2xl text-gray-300 mb-8 leading-relaxed">
            Deploy with confidence. Automate vulnerability detection & compliance checks before your code hits the blockchain.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <button
              onClick={() => scrollToSection('demo')}
              className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-lg text-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition-all transform hover:scale-105 shadow-lg"
            >
              ⚡ Get Your Free Code Scan Today
            </button>
            <button
              onClick={() => setShowDemo(true)}
              className="border border-gray-400 text-gray-300 px-8 py-4 rounded-lg text-lg font-semibold hover:bg-white hover:text-gray-900 transition-all"
            >
              Watch Demo
            </button>
          </div>
        </div>
      </section>

      {/* Key Features */}
      <section id="features" className="container mx-auto px-6 py-20">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-4xl font-bold text-white text-center mb-16">
            Why Choose SecureAudit?
          </h2>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-slate-800/50 backdrop-blur-sm p-8 rounded-xl border border-slate-700 hover:border-blue-500 transition-all">
              <div className="text-4xl mb-4">🔍</div>
              <h3 className="text-xl font-bold text-white mb-4">AI-Driven Code Analysis</h3>
              <p className="text-gray-300">
                Faster than manual audits, with higher accuracy. Our AI detects vulnerabilities that human auditors might miss.
              </p>
            </div>
            <div className="bg-slate-800/50 backdrop-blur-sm p-8 rounded-xl border border-slate-700 hover:border-purple-500 transition-all">
              <div className="text-4xl mb-4">🚀</div>
              <h3 className="text-xl font-bold text-white mb-4">Continuous Security Monitoring</h3>
              <p className="text-gray-300">
                Real-time alerts as your code evolves. Stay protected with ongoing monitoring and instant notifications.
              </p>
            </div>
            <div className="bg-slate-800/50 backdrop-blur-sm p-8 rounded-xl border border-slate-700 hover:border-green-500 transition-all">
              <div className="text-4xl mb-4">🔐</div>
              <h3 className="text-xl font-bold text-white mb-4">Compliance Reporting</h3>
              <p className="text-gray-300">
                Automatic generation of audit reports for regulators & investors. Meet compliance requirements effortlessly.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Technical Specifications */}
      <section className="container mx-auto px-6 py-20 bg-slate-800/20">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-4xl font-bold text-white text-center mb-16">
            Technical Specifications
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Supported Languages */}
            <div className="bg-slate-800/50 backdrop-blur-sm p-6 rounded-xl border border-slate-700">
              <div className="text-3xl mb-4">⚡</div>
              <h3 className="text-lg font-bold text-white mb-4">Languages</h3>
              <ul className="space-y-2 text-gray-300 text-sm">
                <li>• Solidity (all versions)</li>
                <li>• Vyper</li>
                <li>• Rust (Solana)</li>
                <li>• Move (Aptos/Sui)</li>
                <li>• Cairo (StarkNet)</li>
              </ul>
            </div>

            {/* Supported Blockchains */}
            <div className="bg-slate-800/50 backdrop-blur-sm p-6 rounded-xl border border-slate-700">
              <div className="text-3xl mb-4">🔗</div>
              <h3 className="text-lg font-bold text-white mb-4">Blockchains</h3>
              <ul className="space-y-2 text-gray-300 text-sm">
                <li>• Ethereum & L2s</li>
                <li>• Polygon, BSC</li>
                <li>• Arbitrum, Optimism</li>
                <li>• Solana, Avalanche</li>
                <li>• 15+ more networks</li>
              </ul>
            </div>

            {/* Vulnerability Types */}
            <div className="bg-slate-800/50 backdrop-blur-sm p-6 rounded-xl border border-slate-700">
              <div className="text-3xl mb-4">🛡️</div>
              <h3 className="text-lg font-bold text-white mb-4">Detects</h3>
              <ul className="space-y-2 text-gray-300 text-sm">
                <li>• Reentrancy attacks</li>
                <li>• Integer overflow/underflow</li>
                <li>• Access control issues</li>
                <li>• Logic vulnerabilities</li>
                <li>• Gas optimization</li>
              </ul>
            </div>

            {/* Integration Options */}
            <div className="bg-slate-800/50 backdrop-blur-sm p-6 rounded-xl border border-slate-700">
              <div className="text-3xl mb-4">🔧</div>
              <h3 className="text-lg font-bold text-white mb-4">Integrations</h3>
              <ul className="space-y-2 text-gray-300 text-sm">
                <li>• GitHub Actions</li>
                <li>• VS Code Extension</li>
                <li>• CLI Tools</li>
                <li>• REST API</li>
                <li>• Webhook Support</li>
              </ul>
            </div>
          </div>

          {/* Compliance & Security */}
          <div className="mt-12 grid md:grid-cols-2 gap-8">
            <div className="bg-slate-800/50 backdrop-blur-sm p-8 rounded-xl border border-slate-700">
              <h3 className="text-xl font-bold text-white mb-6 flex items-center">
                <span className="text-2xl mr-3">🔒</span>
                Security & Compliance
              </h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <div className="text-green-400 font-semibold mb-2">Certifications</div>
                  <ul className="space-y-1 text-gray-300">
                    <li>• SOC2 Type II</li>
                    <li>• ISO 27001</li>
                    <li>• GDPR Compliant</li>
                  </ul>
                </div>
                <div>
                  <div className="text-blue-400 font-semibold mb-2">Security</div>
                  <ul className="space-y-1 text-gray-300">
                    <li>• End-to-end encryption</li>
                    <li>• Zero-knowledge analysis</li>
                    <li>• No code storage</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="bg-slate-800/50 backdrop-blur-sm p-8 rounded-xl border border-slate-700">
              <h3 className="text-xl font-bold text-white mb-6 flex items-center">
                <span className="text-2xl mr-3">📊</span>
                Performance Metrics
              </h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <div className="text-purple-400 font-semibold mb-2">Speed</div>
                  <ul className="space-y-1 text-gray-300">
                    <li>• &lt;2s analysis time</li>
                    <li>• 99.9% uptime SLA</li>
                    <li>• Real-time results</li>
                  </ul>
                </div>
                <div>
                  <div className="text-yellow-400 font-semibold mb-2">Accuracy</div>
                  <ul className="space-y-1 text-gray-300">
                    <li>• 94% detection rate</li>
                    <li>• &lt;0.1% false positives</li>
                    <li>• Continuous learning</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Value Proposition */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl font-bold text-white mb-8">
            Cut weeks of manual code review into{" "}
            <span className="bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent">
              minutes
            </span>
          </h2>
          <p className="text-xl text-gray-300 mb-12">
            Reduce costly vulnerabilities and protect your users' assets with our comprehensive security solution.
          </p>
          <div className="grid md:grid-cols-3 gap-8 text-center">
            <div>
              <div className="text-3xl font-bold text-blue-400 mb-2">95%</div>
              <div className="text-gray-300">Faster than manual audits</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-purple-400 mb-2">$2M+</div>
              <div className="text-gray-300">In vulnerabilities prevented</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-green-400 mb-2">500+</div>
              <div className="text-gray-300">Smart contracts secured</div>
            </div>
          </div>
        </div>
      </section>

      {/* Pain Points */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-4xl font-bold text-white text-center mb-16">
            Stop Struggling With Traditional Audits
          </h2>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="bg-red-500/20 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">⏰</span>
              </div>
              <h3 className="text-xl font-bold text-white mb-4">Slow & Expensive Manual Audits</h3>
              <p className="text-gray-300">
                Traditional audits take weeks and cost thousands. Time is money in the fast-moving Web3 space.
              </p>
            </div>
            <div className="text-center">
              <div className="bg-red-500/20 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">⚠️</span>
              </div>
              <h3 className="text-xl font-bold text-white mb-4">Fear of Critical Vulnerabilities</h3>
              <p className="text-gray-300">
                One missed vulnerability can cost millions. The stakes are too high to rely on manual reviews alone.
              </p>
            </div>
            <div className="text-center">
              <div className="bg-red-500/20 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">📋</span>
              </div>
              <h3 className="text-xl font-bold text-white mb-4">Complex Compliance Requirements</h3>
              <p className="text-gray-300">
                Navigating regulatory requirements is complex and time-consuming. Miss something and face legal issues.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Social Proof */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-2xl font-bold text-white mb-12">
            Trusted by leading DeFi platforms, NFT marketplaces, and DAOs worldwide
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 items-center opacity-60">
            <div className="bg-slate-700 h-16 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold">UniSwap</span>
            </div>
            <div className="bg-slate-700 h-16 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold">OpenSea</span>
            </div>
            <div className="bg-slate-700 h-16 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold">Compound</span>
            </div>
            <div className="bg-slate-700 h-16 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold">Aave</span>
            </div>
          </div>
        </div>
      </section>

      {/* Customer Testimonials */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-4xl font-bold text-white text-center mb-16">
            What Our Customers Say
          </h2>
          <div className="grid md:grid-cols-3 gap-8">
            {/* Testimonial 1 */}
            <div className="bg-slate-800/50 backdrop-blur-sm p-8 rounded-xl border border-slate-700 hover:border-blue-500 transition-all">
              <div className="flex items-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <span key={i} className="text-yellow-400 text-xl">★</span>
                ))}
              </div>
              <blockquote className="text-gray-300 mb-6 leading-relaxed">
                "SecureAudit caught a critical reentrancy vulnerability that could have cost us $2.3M. Their AI analysis is incredibly thorough and saved our DeFi protocol from a potential disaster."
              </blockquote>
              <div className="flex items-center">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mr-4">
                  <span className="text-white font-bold text-lg">AS</span>
                </div>
                <div>
                  <div className="text-white font-semibold">Alex Chen</div>
                  <div className="text-gray-400 text-sm">CTO, DeFiVault Protocol</div>
                </div>
              </div>
            </div>

            {/* Testimonial 2 */}
            <div className="bg-slate-800/50 backdrop-blur-sm p-8 rounded-xl border border-slate-700 hover:border-purple-500 transition-all">
              <div className="flex items-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <span key={i} className="text-yellow-400 text-xl">★</span>
                ))}
              </div>
              <blockquote className="text-gray-300 mb-6 leading-relaxed">
                "We've been using SecureAudit for 6 months. The real-time monitoring caught 3 vulnerabilities in our smart contracts before deployment. Best investment we've made for security."
              </blockquote>
              <div className="flex items-center">
                <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-blue-600 rounded-full flex items-center justify-center mr-4">
                  <span className="text-white font-bold text-lg">MR</span>
                </div>
                <div>
                  <div className="text-white font-semibold">Maria Rodriguez</div>
                  <div className="text-gray-400 text-sm">Lead Developer, NFT Marketplace</div>
                </div>
              </div>
            </div>

            {/* Testimonial 3 */}
            <div className="bg-slate-800/50 backdrop-blur-sm p-8 rounded-xl border border-slate-700 hover:border-green-500 transition-all">
              <div className="flex items-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <span key={i} className="text-yellow-400 text-xl">★</span>
                ))}
              </div>
              <blockquote className="text-gray-300 mb-6 leading-relaxed">
                "The gas optimization suggestions alone saved us 40% on deployment costs. SecureAudit pays for itself and the security insights are invaluable for our enterprise clients."
              </blockquote>
              <div className="flex items-center">
                <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full flex items-center justify-center mr-4">
                  <span className="text-white font-bold text-lg">DJ</span>
                </div>
                <div>
                  <div className="text-white font-semibold">David Kim</div>
                  <div className="text-gray-400 text-sm">Security Lead, Enterprise DAO</div>
                </div>
              </div>
            </div>
          </div>

          {/* Additional testimonial stats */}
          <div className="mt-16 text-center">
            <div className="grid md:grid-cols-4 gap-8">
              <div>
                <div className="text-3xl font-bold text-blue-400 mb-2">4.9/5</div>
                <div className="text-gray-300">Average Rating</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-purple-400 mb-2">1,200+</div>
                <div className="text-gray-300">Happy Customers</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-green-400 mb-2">$50M+</div>
                <div className="text-gray-300">Assets Protected</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-yellow-400 mb-2">99.2%</div>
                <div className="text-gray-300">Customer Satisfaction</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="container mx-auto px-6 py-20">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-4xl font-bold text-white text-center mb-16">
            Simple, Transparent Pricing
          </h2>
          <div className="grid md:grid-cols-3 gap-8">
            {/* Free Tier */}
            <div className="bg-slate-800/50 backdrop-blur-sm p-8 rounded-xl border border-slate-700 hover:border-blue-500 transition-all">
              <div className="text-center">
                <h3 className="text-2xl font-bold text-white mb-4">Free Scan</h3>
                <div className="text-4xl font-bold text-blue-400 mb-2">$0</div>
                <p className="text-gray-400 mb-6">Perfect for testing</p>
                <ul className="text-left space-y-3 mb-8">
                  <li className="flex items-center text-gray-300">
                    <span className="text-green-400 mr-2">✓</span>
                    1 contract scan per month
                  </li>
                  <li className="flex items-center text-gray-300">
                    <span className="text-green-400 mr-2">✓</span>
                    Basic vulnerability detection
                  </li>
                  <li className="flex items-center text-gray-300">
                    <span className="text-green-400 mr-2">✓</span>
                    PDF report
                  </li>
                  <li className="flex items-center text-gray-300">
                    <span className="text-gray-500 mr-2">✗</span>
                    Real-time monitoring
                  </li>
                </ul>
                <button className="w-full bg-blue-600 text-white py-3 rounded-lg hover:bg-blue-700 transition-colors">
                  Start Free Scan
                </button>
              </div>
            </div>

            {/* Pro Tier */}
            <div className="bg-gradient-to-b from-purple-600/20 to-blue-600/20 backdrop-blur-sm p-8 rounded-xl border border-purple-500 relative transform scale-105">
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <span className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-4 py-1 rounded-full text-sm font-semibold">
                  Most Popular
                </span>
              </div>
              <div className="text-center">
                <h3 className="text-2xl font-bold text-white mb-4">Pro</h3>
                <div className="text-4xl font-bold text-purple-400 mb-2">$99</div>
                <p className="text-gray-400 mb-6">per month</p>
                <ul className="text-left space-y-3 mb-8">
                  <li className="flex items-center text-gray-300">
                    <span className="text-green-400 mr-2">✓</span>
                    Unlimited contract scans
                  </li>
                  <li className="flex items-center text-gray-300">
                    <span className="text-green-400 mr-2">✓</span>
                    Advanced AI analysis
                  </li>
                  <li className="flex items-center text-gray-300">
                    <span className="text-green-400 mr-2">✓</span>
                    Real-time monitoring
                  </li>
                  <li className="flex items-center text-gray-300">
                    <span className="text-green-400 mr-2">✓</span>
                    Gas optimization suggestions
                  </li>
                  <li className="flex items-center text-gray-300">
                    <span className="text-green-400 mr-2">✓</span>
                    Priority support
                  </li>
                </ul>
                <button className="w-full bg-gradient-to-r from-purple-600 to-blue-600 text-white py-3 rounded-lg hover:from-purple-700 hover:to-blue-700 transition-all">
                  Start Pro Trial
                </button>
              </div>
            </div>

            {/* Enterprise Tier */}
            <div className="bg-slate-800/50 backdrop-blur-sm p-8 rounded-xl border border-slate-700 hover:border-green-500 transition-all">
              <div className="text-center">
                <h3 className="text-2xl font-bold text-white mb-4">Enterprise</h3>
                <div className="text-4xl font-bold text-green-400 mb-2">Custom</div>
                <p className="text-gray-400 mb-6">for large teams</p>
                <ul className="text-left space-y-3 mb-8">
                  <li className="flex items-center text-gray-300">
                    <span className="text-green-400 mr-2">✓</span>
                    Everything in Pro
                  </li>
                  <li className="flex items-center text-gray-300">
                    <span className="text-green-400 mr-2">✓</span>
                    Custom integrations
                  </li>
                  <li className="flex items-center text-gray-300">
                    <span className="text-green-400 mr-2">✓</span>
                    Dedicated support
                  </li>
                  <li className="flex items-center text-gray-300">
                    <span className="text-green-400 mr-2">✓</span>
                    SLA guarantees
                  </li>
                  <li className="flex items-center text-gray-300">
                    <span className="text-green-400 mr-2">✓</span>
                    On-premise deployment
                  </li>
                </ul>
                <button className="w-full bg-green-600 text-white py-3 rounded-lg hover:bg-green-700 transition-colors">
                  Contact Sales
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section id="faq" className="container mx-auto px-6 py-20">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-4xl font-bold text-white text-center mb-16">
            Frequently Asked Questions
          </h2>
          <div className="space-y-4">
            {[
              {
                question: "How secure is your AI analysis platform?",
                answer: "Our platform uses enterprise-grade security with SOC2 Type II compliance, end-to-end encryption, and zero-knowledge architecture. Your smart contracts are analyzed in isolated environments and never stored permanently. We're audited by leading cybersecurity firms and maintain the highest industry standards."
              },
              {
                question: "What smart contract languages and blockchains do you support?",
                answer: "We support Solidity, Vyper, Rust (for Solana), and Move (for Aptos/Sui). Our platform works with Ethereum, Polygon, BSC, Arbitrum, Optimism, Solana, and 15+ other major blockchains. We're constantly adding support for new languages and networks based on community demand."
              },
              {
                question: "How accurate is your AI compared to manual audits?",
                answer: "Our AI achieves 94% accuracy in vulnerability detection, compared to 78% for manual audits alone. We combine multiple AI models with rule-based analysis and have been trained on over 100,000 audited contracts. However, we recommend combining our AI analysis with expert review for critical applications."
              },
              {
                question: "What happens after my free scan?",
                answer: "After your free scan, you'll receive a detailed PDF report with vulnerability findings, risk assessment, and gas optimization suggestions. You can upgrade to Pro for unlimited scans, real-time monitoring, and priority support. No credit card required for the free tier."
              },
              {
                question: "How does this integrate with my development workflow?",
                answer: "We offer multiple integration options: GitHub Actions for CI/CD, CLI tools for local development, VS Code extension, and REST API for custom integrations. You can set up automated scanning on every commit, pull request, or deployment."
              },
              {
                question: "Do you provide compliance reports for regulators?",
                answer: "Yes, our Pro and Enterprise plans include compliance reporting for SOX, PCI DSS, and emerging DeFi regulations. Reports include executive summaries, technical findings, remediation timelines, and audit trails suitable for regulatory submission."
              },
              {
                question: "What's your SLA for Enterprise customers?",
                answer: "Enterprise customers receive 99.9% uptime SLA, <2 second analysis response times, 24/7 dedicated support, and guaranteed vulnerability detection accuracy. We also offer on-premise deployment and custom integration support."
              },
              {
                question: "Can you detect zero-day vulnerabilities?",
                answer: "Our AI models are continuously updated with the latest vulnerability patterns and can detect novel attack vectors by analyzing code patterns and logic flows. We've successfully identified several zero-day vulnerabilities before they were publicly disclosed."
              }
            ].map((faq, index) => (
              <div key={index} className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700 overflow-hidden">
                <button
                  onClick={() => setOpenFaq(openFaq === index ? null : index)}
                  className="w-full px-8 py-6 text-left flex justify-between items-center hover:bg-slate-700/30 transition-colors"
                >
                  <h3 className="text-lg font-semibold text-white pr-4">{faq.question}</h3>
                  <span className={`text-2xl text-blue-400 transition-transform ${openFaq === index ? 'rotate-45' : ''}`}>
                    +
                  </span>
                </button>
                {openFaq === index && (
                  <div className="px-8 pb-6">
                    <p className="text-gray-300 leading-relaxed">{faq.answer}</p>
                  </div>
                )}
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <p className="text-gray-400 mb-4">Still have questions?</p>
            <button
              onClick={() => scrollToSection('demo')}
              className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all"
            >
              Contact Our Security Experts
            </button>
          </div>
        </div>
      </section>

      {/* Interactive Demo Section */}
      <section id="demo" className="container mx-auto px-6 py-20">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-4xl font-bold text-white text-center mb-8">
            Try Our AI-Powered Smart Contract Scanner
          </h2>
          <p className="text-xl text-gray-300 text-center mb-12">
            Upload your smart contract and get instant security analysis
          </p>

          <div className="bg-slate-800/50 backdrop-blur-sm p-8 rounded-xl border border-slate-700">
            <div className="text-center">
              <div className="border-2 border-dashed border-gray-600 rounded-lg p-12 mb-6 hover:border-blue-500 transition-colors">
                <input
                  type="file"
                  accept=".sol,.js,.ts"
                  onChange={handleFileUpload}
                  className="hidden"
                  id="file-upload"
                />
                <label htmlFor="file-upload" className="cursor-pointer">
                  <div className="text-6xl mb-4">📄</div>
                  <p className="text-xl text-white mb-2">Drop your smart contract here</p>
                  <p className="text-gray-400">or click to browse (.sol, .js, .ts files)</p>
                </label>
              </div>

              {isUploading && (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
                  <p className="text-white">Analyzing your smart contract...</p>
                  <p className="text-gray-400 text-sm">This may take a few moments</p>
                </div>
              )}

              {uploadResult && (
                <div className="bg-slate-700/50 rounded-lg p-6 text-left">
                  <h3 className="text-xl font-bold text-white mb-4">
                    📊 Analysis Results for {uploadResult.fileName}
                  </h3>
                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <div className="mb-4">
                        <span className="text-gray-400">Security Score:</span>
                        <div className="flex items-center mt-1">
                          <div className="bg-gray-700 rounded-full h-2 flex-1 mr-3">
                            <div
                              className="bg-gradient-to-r from-green-500 to-blue-500 h-2 rounded-full"
                              style={{ width: `${uploadResult.score}%` }}
                            ></div>
                          </div>
                          <span className="text-white font-bold">{uploadResult.score}/100</span>
                        </div>
                      </div>
                      <div className="mb-4">
                        <span className="text-gray-400">Vulnerabilities Found:</span>
                        <span className={`ml-2 font-bold ${uploadResult.vulnerabilities > 2 ? 'text-red-400' : uploadResult.vulnerabilities > 0 ? 'text-yellow-400' : 'text-green-400'}`}>
                          {uploadResult.vulnerabilities}
                        </span>
                      </div>
                    </div>
                    <div>
                      <div className="mb-4">
                        <span className="text-gray-400">Risk Level:</span>
                        <span className={`ml-2 font-bold ${uploadResult.riskLevel === 'High' ? 'text-red-400' : uploadResult.riskLevel === 'Medium' ? 'text-yellow-400' : 'text-green-400'}`}>
                          {uploadResult.riskLevel}
                        </span>
                      </div>
                      <div className="mb-4">
                        <span className="text-gray-400">Gas Optimization:</span>
                        <span className="ml-2 font-bold text-blue-400">{uploadResult.gasOptimization}% savings possible</span>
                      </div>
                    </div>
                  </div>
                  <div className="mt-6 pt-6 border-t border-slate-600">
                    <button className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all mr-4">
                      Download Full Report
                    </button>
                    <button className="border border-gray-400 text-gray-300 px-6 py-3 rounded-lg hover:bg-white hover:text-gray-900 transition-all">
                      Schedule Expert Review
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Contact Form Section */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-2xl mx-auto">
          <h2 className="text-4xl font-bold text-white text-center mb-8">
            Request a Demo
          </h2>
          <p className="text-xl text-gray-300 text-center mb-12">
            Get a personalized demo and see how SecureAudit can protect your smart contracts
          </p>

          <form onSubmit={handleFormSubmit} className="bg-slate-800/50 backdrop-blur-sm p-8 rounded-xl border border-slate-700">
            <div className="grid md:grid-cols-2 gap-6 mb-6">
              <div>
                <label className="block text-white mb-2">Name *</label>
                <input
                  type="text"
                  required
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  className="w-full bg-slate-700 text-white px-4 py-3 rounded-lg border border-slate-600 focus:border-blue-500 focus:outline-none"
                  placeholder="Your full name"
                />
              </div>
              <div>
                <label className="block text-white mb-2">Email *</label>
                <input
                  type="email"
                  required
                  value={formData.email}
                  onChange={(e) => setFormData({...formData, email: e.target.value})}
                  className="w-full bg-slate-700 text-white px-4 py-3 rounded-lg border border-slate-600 focus:border-blue-500 focus:outline-none"
                  placeholder="<EMAIL>"
                />
              </div>
            </div>
            <div className="mb-6">
              <label className="block text-white mb-2">Company</label>
              <input
                type="text"
                value={formData.company}
                onChange={(e) => setFormData({...formData, company: e.target.value})}
                className="w-full bg-slate-700 text-white px-4 py-3 rounded-lg border border-slate-600 focus:border-blue-500 focus:outline-none"
                placeholder="Your company name"
              />
            </div>
            <div className="mb-6">
              <label className="block text-white mb-2">Message</label>
              <textarea
                rows="4"
                value={formData.message}
                onChange={(e) => setFormData({...formData, message: e.target.value})}
                className="w-full bg-slate-700 text-white px-4 py-3 rounded-lg border border-slate-600 focus:border-blue-500 focus:outline-none"
                placeholder="Tell us about your project and security needs..."
              ></textarea>
            </div>
            <button
              type="submit"
              disabled={isSubmitting}
              className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-4 rounded-lg text-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition-all disabled:opacity-50"
            >
              {isSubmitting ? 'Sending...' : 'Request Demo'}
            </button>
          </form>
        </div>
      </section>

      {/* Final CTA */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-4xl mx-auto text-center bg-gradient-to-r from-blue-600/20 to-purple-600/20 backdrop-blur-sm p-12 rounded-2xl border border-slate-700">
          <h2 className="text-4xl font-bold text-white mb-6">
            Ready to Secure Your Smart Contracts?
          </h2>
          <p className="text-xl text-gray-300 mb-8">
            Join hundreds of developers who trust SecureAudit to protect their code and users.
          </p>
          <button
            onClick={() => scrollToSection('demo')}
            className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-12 py-4 rounded-lg text-xl font-semibold hover:from-blue-700 hover:to-purple-700 transition-all transform hover:scale-105 shadow-lg"
          >
            Scan My Contract Now
          </button>
          <p className="text-sm text-gray-400 mt-4">
            Free scan • No credit card required • Results in minutes
          </p>
        </div>
      </section>

      {/* Footer */}
      <footer className="container mx-auto px-6 py-12 border-t border-slate-700">
        <div className="flex flex-col md:flex-row justify-between items-center">
          <div className="flex items-center space-x-2 mb-4 md:mb-0">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-lg">🛡️</span>
            </div>
            <span className="text-white font-bold text-xl">SecureAudit</span>
          </div>
          <div className="flex space-x-6 text-gray-400">
            <a href="/privacy" className="hover:text-white transition-colors">Privacy</a>
            <a href="/terms" className="hover:text-white transition-colors">Terms</a>
            <a href="/contact" className="hover:text-white transition-colors">Contact</a>
            <a href="/about" className="hover:text-white transition-colors">About</a>
          </div>
        </div>
        <div className="text-center text-gray-400 mt-8">
          © 2024 SecureAudit. All rights reserved.
        </div>
      </footer>

      {/* Demo Modal */}
      {showDemo && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-slate-800 rounded-xl p-8 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-2xl font-bold text-white">SecureAudit Demo</h3>
              <button
                onClick={() => setShowDemo(false)}
                className="text-gray-400 hover:text-white text-2xl"
              >
                ×
              </button>
            </div>

            <div className="aspect-video bg-slate-700 rounded-lg mb-6 flex items-center justify-center">
              <div className="text-center">
                <div className="text-6xl mb-4">🎥</div>
                <p className="text-white text-lg mb-2">Demo Video</p>
                <p className="text-gray-400">See SecureAudit in action</p>
                <button className="mt-4 bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                  ▶ Play Demo
                </button>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="text-lg font-semibold text-white">What you'll see in this demo:</h4>
              <ul className="space-y-2 text-gray-300">
                <li className="flex items-center">
                  <span className="text-green-400 mr-2">✓</span>
                  Upload and scan a real smart contract
                </li>
                <li className="flex items-center">
                  <span className="text-green-400 mr-2">✓</span>
                  AI-powered vulnerability detection in action
                </li>
                <li className="flex items-center">
                  <span className="text-green-400 mr-2">✓</span>
                  Detailed security report generation
                </li>
                <li className="flex items-center">
                  <span className="text-green-400 mr-2">✓</span>
                  Gas optimization recommendations
                </li>
                <li className="flex items-center">
                  <span className="text-green-400 mr-2">✓</span>
                  Real-time monitoring dashboard
                </li>
              </ul>
            </div>

            <div className="mt-8 flex gap-4">
              <button
                onClick={() => {
                  setShowDemo(false);
                  scrollToSection('demo');
                }}
                className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all"
              >
                Try Live Demo
              </button>
              <button
                onClick={() => setShowDemo(false)}
                className="flex-1 border border-gray-400 text-gray-300 py-3 rounded-lg hover:bg-white hover:text-gray-900 transition-all"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
