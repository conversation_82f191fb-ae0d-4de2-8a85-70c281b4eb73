import { NextResponse } from 'next/server';
import prisma from '../../../lib/db.js';
import { getUserFromToken } from '../../../lib/auth.js';

// Smart contract vulnerability patterns for basic static analysis
const VULNERABILITY_PATTERNS = {
  reentrancy: {
    pattern: /\.call\s*\(|\.send\s*\(|\.transfer\s*\(/gi,
    severity: 'High',
    description: 'Potential reentrancy vulnerability detected'
  },
  uncheckedCall: {
    pattern: /\.call\s*\([^)]*\)\s*(?!\.require|;)/gi,
    severity: 'Medium',
    description: 'Unchecked external call return value'
  },
  integerOverflow: {
    pattern: /\+\+|\-\-|\+\s*=|\-\s*=|\*\s*=|\/\s*=/gi,
    severity: 'Medium',
    description: 'Potential integer overflow/underflow'
  },
  accessControl: {
    pattern: /onlyOwner|require\s*\(\s*msg\.sender\s*==|modifier/gi,
    severity: 'Low',
    description: 'Access control pattern detected - verify implementation'
  },
  gasLimit: {
    pattern: /\.gas\s*\(|gasleft\(\)|block\.gaslimit/gi,
    severity: 'Low',
    description: 'Gas limit dependency detected'
  },
  timestamp: {
    pattern: /block\.timestamp|now\s/gi,
    severity: 'Medium',
    description: 'Timestamp dependency detected'
  },
  randomness: {
    pattern: /block\.blockhash|block\.difficulty|keccak256.*block/gi,
    severity: 'High',
    description: 'Weak randomness source detected'
  }
};

// Gas optimization patterns
const GAS_OPTIMIZATION_PATTERNS = {
  publicToExternal: {
    pattern: /function\s+\w+\s*\([^)]*\)\s+public/gi,
    suggestion: 'Consider using external instead of public for functions not called internally',
    savings: 5
  },
  storageToMemory: {
    pattern: /storage\s+\w+/gi,
    suggestion: 'Consider using memory instead of storage for temporary variables',
    savings: 15
  },
  constantVariables: {
    pattern: /uint256\s+\w+\s*=\s*\d+/gi,
    suggestion: 'Consider making constant values immutable or constant',
    savings: 10
  },
  loopOptimization: {
    pattern: /for\s*\([^)]*\.length[^)]*\)/gi,
    suggestion: 'Cache array length in loops to save gas',
    savings: 8
  }
};

export async function POST(request) {
  try {
    // Get user from token (optional for free scans)
    let user = null;
    const authHeader = request.headers.get('authorization');
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      user = await getUserFromToken(token);
    }

    const formData = await request.formData();
    const file = formData.get('file');

    if (!file) {
      return NextResponse.json(
        { error: 'No file uploaded' },
        { status: 400 }
      );
    }

    // Validate file type
    const allowedTypes = ['.sol', '.vy', '.rs', '.move'];
    const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));

    if (!allowedTypes.includes(fileExtension)) {
      return NextResponse.json(
        { error: 'Unsupported file type. Please upload .sol, .vy, .rs, or .move files.' },
        { status: 400 }
      );
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      return NextResponse.json(
        { error: 'File too large. Maximum size is 5MB.' },
        { status: 400 }
      );
    }

    // Read file content
    const fileContent = await file.text();

    // Validate file content
    if (!fileContent.trim()) {
      return NextResponse.json(
        { error: 'File appears to be empty' },
        { status: 400 }
      );
    }

    // Perform static analysis
    const analysisResult = await analyzeSmartContract(fileContent, file.name);
    const analysisId = generateAnalysisId();

    // Save to database if user is authenticated
    if (user) {
      try {
        await prisma.scan.create({
          data: {
            userId: user.id,
            fileName: file.name,
            fileSize: file.size,
            fileContent: fileContent, // Store for re-analysis
            analysisId: analysisId,
            securityScore: analysisResult.securityScore,
            riskLevel: analysisResult.riskLevel.toUpperCase(),
            vulnerabilities: JSON.stringify(analysisResult.vulnerabilities),
            gasOptimizations: JSON.stringify(analysisResult.gasOptimizations),
            complianceChecks: JSON.stringify(analysisResult.complianceChecks),
            codeMetrics: JSON.stringify(analysisResult.codeMetrics),
          },
        });
      } catch (dbError) {
        console.error('Database save error:', dbError);
        // Continue with response even if DB save fails
      }
    }

    // Log analysis for monitoring
    console.log('Smart contract analysis:', {
      timestamp: new Date().toISOString(),
      userId: user?.id || 'anonymous',
      fileName: file.name,
      fileSize: file.size,
      vulnerabilities: analysisResult.vulnerabilities.length,
      riskLevel: analysisResult.riskLevel
    });

    return NextResponse.json({
      success: true,
      fileName: file.name,
      fileSize: file.size,
      analysisId: analysisId,
      ...analysisResult
    });

  } catch (error) {
    console.error('Analysis error:', error);
    return NextResponse.json(
      { error: 'Failed to analyze smart contract. Please try again.' },
      { status: 500 }
    );
  }
}

async function analyzeSmartContract(content, fileName) {
  // Simulate analysis time (remove in production)
  await new Promise(resolve => setTimeout(resolve, 1000));

  const vulnerabilities = [];
  const gasOptimizations = [];
  let totalGasSavings = 0;

  // Check for vulnerability patterns
  Object.entries(VULNERABILITY_PATTERNS).forEach(([type, config]) => {
    const matches = content.match(config.pattern);
    if (matches) {
      vulnerabilities.push({
        type,
        severity: config.severity,
        description: config.description,
        occurrences: matches.length,
        lines: findLineNumbers(content, config.pattern)
      });
    }
  });

  // Check for gas optimization opportunities
  Object.entries(GAS_OPTIMIZATION_PATTERNS).forEach(([type, config]) => {
    const matches = content.match(config.pattern);
    if (matches) {
      const savings = config.savings * matches.length;
      totalGasSavings += savings;
      gasOptimizations.push({
        type,
        suggestion: config.suggestion,
        occurrences: matches.length,
        potentialSavings: savings,
        lines: findLineNumbers(content, config.pattern)
      });
    }
  });

  // Calculate risk level
  const riskLevel = calculateRiskLevel(vulnerabilities);
  
  // Calculate security score
  const securityScore = calculateSecurityScore(vulnerabilities, content.length);

  // Generate compliance status
  const complianceChecks = performComplianceChecks(content);

  return {
    vulnerabilities,
    gasOptimizations,
    riskLevel,
    securityScore,
    gasOptimization: Math.min(totalGasSavings, 50), // Cap at 50%
    complianceChecks,
    analysisTimestamp: new Date().toISOString(),
    codeMetrics: {
      linesOfCode: content.split('\n').length,
      functions: (content.match(/function\s+\w+/gi) || []).length,
      contracts: (content.match(/contract\s+\w+/gi) || []).length,
      modifiers: (content.match(/modifier\s+\w+/gi) || []).length
    }
  };
}

function findLineNumbers(content, pattern) {
  const lines = content.split('\n');
  const lineNumbers = [];
  
  lines.forEach((line, index) => {
    if (pattern.test(line)) {
      lineNumbers.push(index + 1);
    }
  });
  
  return lineNumbers.slice(0, 5); // Return first 5 matches
}

function calculateRiskLevel(vulnerabilities) {
  const highSeverity = vulnerabilities.filter(v => v.severity === 'High').length;
  const mediumSeverity = vulnerabilities.filter(v => v.severity === 'Medium').length;
  
  if (highSeverity > 0) return 'High';
  if (mediumSeverity > 2) return 'High';
  if (mediumSeverity > 0) return 'Medium';
  return 'Low';
}

function calculateSecurityScore(vulnerabilities, codeLength) {
  let score = 100;
  
  vulnerabilities.forEach(vuln => {
    switch (vuln.severity) {
      case 'High':
        score -= 15 * vuln.occurrences;
        break;
      case 'Medium':
        score -= 8 * vuln.occurrences;
        break;
      case 'Low':
        score -= 3 * vuln.occurrences;
        break;
    }
  });
  
  // Bonus for longer, more complex contracts that pass analysis
  if (codeLength > 1000 && score > 80) {
    score += 5;
  }
  
  return Math.max(score, 0);
}

function performComplianceChecks(content) {
  const checks = [
    {
      name: 'OWASP Smart Contract Top 10',
      status: content.includes('require(') ? 'Partial' : 'Failed',
      description: 'Input validation and error handling'
    },
    {
      name: 'Access Control',
      status: content.includes('onlyOwner') || content.includes('modifier') ? 'Passed' : 'Warning',
      description: 'Proper access control mechanisms'
    },
    {
      name: 'Reentrancy Protection',
      status: content.includes('nonReentrant') || !content.includes('.call(') ? 'Passed' : 'Failed',
      description: 'Protection against reentrancy attacks'
    }
  ];
  
  return checks;
}

function generateAnalysisId() {
  return 'SA_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}
