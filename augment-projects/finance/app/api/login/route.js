import { NextResponse } from 'next/server';
import crypto from 'crypto';

// Simple in-memory user storage (should match the one in register/route.js)
// In production, use a proper database
const users = new Map();

export async function POST(request) {
  try {
    const body = await request.json();
    const { email, password } = body;

    // Validate required fields
    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      );
    }

    // Find user
    const user = users.get(email.toLowerCase());
    if (!user) {
      return NextResponse.json(
        { error: 'Invalid email or password' },
        { status: 401 }
      );
    }

    // Verify password
    const hashedPassword = crypto.createHash('sha256').update(password).digest('hex');
    if (user.password !== hashedPassword) {
      return NextResponse.json(
        { error: 'Invalid email or password' },
        { status: 401 }
      );
    }

    // Check if account is active
    if (!user.isActive) {
      return NextResponse.json(
        { error: 'Account is deactivated. Please contact support.' },
        { status: 403 }
      );
    }

    // Update last login
    user.lastLogin = new Date().toISOString();

    // Generate session token (in production, use JWT or proper session management)
    const sessionToken = generateSessionToken();

    // Log login for monitoring
    console.log('User login:', {
      timestamp: new Date().toISOString(),
      userId: user.id,
      email: user.email
    });

    // Return success response (don't include password)
    const { password: _, ...userResponse } = user;
    return NextResponse.json({
      success: true,
      message: 'Login successful',
      user: userResponse,
      sessionToken,
      dashboard: '/dashboard'
    });

  } catch (error) {
    console.error('Login error:', error);
    return NextResponse.json(
      { error: 'Login failed. Please try again.' },
      { status: 500 }
    );
  }
}

function generateSessionToken() {
  return 'sess_' + Date.now() + '_' + crypto.randomBytes(16).toString('hex');
}
