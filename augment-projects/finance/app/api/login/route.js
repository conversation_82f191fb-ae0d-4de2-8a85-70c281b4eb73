import { NextResponse } from 'next/server';
import prisma from '../../../lib/db.js';
import { verifyPassword, generateToken, createSession, isValidEmail } from '../../../lib/auth.js';

export async function POST(request) {
  try {
    const body = await request.json();
    const { email, password } = body;

    // Validate required fields
    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      );
    }

    // Validate email format
    if (!isValidEmail(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Find user in database
    const user = await prisma.user.findUnique({
      where: { email: email.toLowerCase() }
    });

    if (!user) {
      return NextResponse.json(
        { error: 'Invalid email or password' },
        { status: 401 }
      );
    }

    // Verify password
    const isPasswordValid = await verifyPassword(password, user.password);
    if (!isPasswordValid) {
      return NextResponse.json(
        { error: 'Invalid email or password' },
        { status: 401 }
      );
    }

    // Check if account is active
    if (!user.isActive) {
      return NextResponse.json(
        { error: 'Account is deactivated. Please contact support.' },
        { status: 403 }
      );
    }

    // Update last login
    await prisma.user.update({
      where: { id: user.id },
      data: { lastLogin: new Date() }
    });

    // Generate JWT token and create session
    const token = generateToken(user);
    await createSession(user.id, token);

    // Log login for monitoring
    console.log('User login:', {
      timestamp: new Date().toISOString(),
      userId: user.id,
      email: user.email
    });

    // Return success response (don't include password)
    const { password: _, ...userResponse } = user;
    return NextResponse.json({
      success: true,
      message: 'Login successful',
      user: userResponse,
      token,
      dashboard: '/dashboard'
    });

  } catch (error) {
    console.error('Login error:', error);
    return NextResponse.json(
      { error: 'Login failed. Please try again.' },
      { status: 500 }
    );
  }
}
