import { NextResponse } from 'next/server';
import nodemailer from 'nodemailer';

// Create reusable transporter object using SMTP transport
const createTransporter = () => {
  // In production, use environment variables for email configuration
  // For demo purposes, we'll simulate email sending
  return nodemailer.createTransporter({
    host: process.env.SMTP_HOST || 'smtp.gmail.com',
    port: process.env.SMTP_PORT || 587,
    secure: false,
    auth: {
      user: process.env.SMTP_USER || '<EMAIL>',
      pass: process.env.SMTP_PASS || 'demo-password'
    }
  });
};

export async function POST(request) {
  try {
    const body = await request.json();
    const { name, email, company, subject, message, inquiryType } = body;

    // Validate required fields
    if (!name || !email || !subject || !message) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Create email content
    const emailContent = {
      from: process.env.SMTP_FROM || '<EMAIL>',
      to: getRecipientEmail(inquiryType),
      cc: '<EMAIL>',
      subject: `[SecureAudit] ${inquiryType.toUpperCase()}: ${subject}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background: linear-gradient(135deg, #1e293b 0%, #7c3aed 100%); padding: 20px; text-align: center;">
            <h1 style="color: white; margin: 0;">🛡️ SecureAudit</h1>
            <p style="color: #e2e8f0; margin: 10px 0 0 0;">New Contact Form Submission</p>
          </div>
          
          <div style="padding: 30px; background: #f8fafc; border: 1px solid #e2e8f0;">
            <h2 style="color: #1e293b; margin-top: 0;">Contact Details</h2>
            
            <table style="width: 100%; border-collapse: collapse;">
              <tr>
                <td style="padding: 10px 0; border-bottom: 1px solid #e2e8f0; font-weight: bold; color: #374151;">Name:</td>
                <td style="padding: 10px 0; border-bottom: 1px solid #e2e8f0; color: #1f2937;">${name}</td>
              </tr>
              <tr>
                <td style="padding: 10px 0; border-bottom: 1px solid #e2e8f0; font-weight: bold; color: #374151;">Email:</td>
                <td style="padding: 10px 0; border-bottom: 1px solid #e2e8f0; color: #1f2937;">${email}</td>
              </tr>
              <tr>
                <td style="padding: 10px 0; border-bottom: 1px solid #e2e8f0; font-weight: bold; color: #374151;">Company:</td>
                <td style="padding: 10px 0; border-bottom: 1px solid #e2e8f0; color: #1f2937;">${company || 'Not provided'}</td>
              </tr>
              <tr>
                <td style="padding: 10px 0; border-bottom: 1px solid #e2e8f0; font-weight: bold; color: #374151;">Inquiry Type:</td>
                <td style="padding: 10px 0; border-bottom: 1px solid #e2e8f0; color: #1f2937;">${inquiryType}</td>
              </tr>
              <tr>
                <td style="padding: 10px 0; border-bottom: 1px solid #e2e8f0; font-weight: bold; color: #374151;">Subject:</td>
                <td style="padding: 10px 0; border-bottom: 1px solid #e2e8f0; color: #1f2937;">${subject}</td>
              </tr>
            </table>
            
            <h3 style="color: #1e293b; margin-top: 30px;">Message</h3>
            <div style="background: white; padding: 20px; border-radius: 8px; border: 1px solid #e2e8f0;">
              <p style="color: #374151; line-height: 1.6; margin: 0;">${message.replace(/\n/g, '<br>')}</p>
            </div>
            
            <div style="margin-top: 30px; padding: 20px; background: #dbeafe; border-radius: 8px; border-left: 4px solid #3b82f6;">
              <h4 style="color: #1e40af; margin: 0 0 10px 0;">Next Steps</h4>
              <p style="color: #1e40af; margin: 0; font-size: 14px;">
                • Respond within ${getResponseTime(inquiryType)}<br>
                • Add to CRM system<br>
                • Schedule follow-up if needed
              </p>
            </div>
          </div>
          
          <div style="padding: 20px; text-align: center; background: #1e293b; color: #94a3b8;">
            <p style="margin: 0; font-size: 12px;">
              This email was generated automatically from the SecureAudit contact form.<br>
              Timestamp: ${new Date().toISOString()}
            </p>
          </div>
        </div>
      `
    };

    // Auto-reply to user
    const autoReplyContent = {
      from: process.env.SMTP_FROM || '<EMAIL>',
      to: email,
      subject: 'Thank you for contacting SecureAudit - We\'ll be in touch soon!',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background: linear-gradient(135deg, #1e293b 0%, #7c3aed 100%); padding: 20px; text-align: center;">
            <h1 style="color: white; margin: 0;">🛡️ SecureAudit</h1>
            <p style="color: #e2e8f0; margin: 10px 0 0 0;">Thank you for your inquiry!</p>
          </div>
          
          <div style="padding: 30px; background: #f8fafc; border: 1px solid #e2e8f0;">
            <h2 style="color: #1e293b; margin-top: 0;">Hi ${name},</h2>
            
            <p style="color: #374151; line-height: 1.6;">
              Thank you for reaching out to SecureAudit! We've received your ${inquiryType} inquiry and our team will respond within <strong>${getResponseTime(inquiryType)}</strong>.
            </p>
            
            <div style="background: #dbeafe; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="color: #1e40af; margin: 0 0 15px 0;">Your Inquiry Summary</h3>
              <p style="color: #1e40af; margin: 0;"><strong>Subject:</strong> ${subject}</p>
              <p style="color: #1e40af; margin: 5px 0 0 0;"><strong>Type:</strong> ${inquiryType}</p>
            </div>
            
            <p style="color: #374151; line-height: 1.6;">
              In the meantime, feel free to:
            </p>
            <ul style="color: #374151; line-height: 1.6;">
              <li>Try our <a href="https://secureaudit.com/#demo" style="color: #3b82f6;">free smart contract scanner</a></li>
              <li>Read our <a href="https://secureaudit.com/about" style="color: #3b82f6;">company story</a></li>
              <li>Check out our <a href="https://secureaudit.com/#faq" style="color: #3b82f6;">frequently asked questions</a></li>
            </ul>
            
            <p style="color: #374151; line-height: 1.6;">
              Best regards,<br>
              <strong>The SecureAudit Team</strong>
            </p>
          </div>
          
          <div style="padding: 20px; text-align: center; background: #1e293b;">
            <p style="color: #94a3b8; margin: 0 0 10px 0; font-size: 14px;">
              <strong>SecureAudit</strong> - AI-Powered Smart Contract Security
            </p>
            <p style="color: #64748b; margin: 0; font-size: 12px;">
              123 Security Blvd, San Francisco, CA 94105<br>
              <a href="https://secureaudit.com" style="color: #3b82f6;">secureaudit.com</a>
            </p>
          </div>
        </div>
      `
    };

    // In production, send actual emails
    // For demo, we'll simulate the email sending
    if (process.env.NODE_ENV === 'production' && process.env.SMTP_HOST) {
      const transporter = createTransporter();
      await transporter.sendMail(emailContent);
      await transporter.sendMail(autoReplyContent);
    }

    // Log the contact submission (in production, save to database)
    console.log('Contact form submission:', {
      timestamp: new Date().toISOString(),
      name,
      email,
      company,
      inquiryType,
      subject
    });

    // Return success response
    return NextResponse.json({
      success: true,
      message: 'Thank you for your message! We\'ll get back to you soon.',
      responseTime: getResponseTime(inquiryType)
    });

  } catch (error) {
    console.error('Contact form error:', error);
    return NextResponse.json(
      { error: 'Failed to send message. Please try again.' },
      { status: 500 }
    );
  }
}

// Helper function to get recipient email based on inquiry type
function getRecipientEmail(inquiryType) {
  const recipients = {
    'general': '<EMAIL>',
    'sales': '<EMAIL>',
    'support': '<EMAIL>',
    'enterprise': '<EMAIL>',
    'partnership': '<EMAIL>',
    'security': '<EMAIL>'
  };
  return recipients[inquiryType] || '<EMAIL>';
}

// Helper function to get response time based on inquiry type
function getResponseTime(inquiryType) {
  const responseTimes = {
    'general': '24 hours',
    'sales': '4 hours',
    'support': '2 hours',
    'enterprise': '2 hours',
    'partnership': '24 hours',
    'security': '1 hour'
  };
  return responseTimes[inquiryType] || '24 hours';
}
