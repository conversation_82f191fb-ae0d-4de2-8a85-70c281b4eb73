import { NextResponse } from 'next/server';
import { invalidateSession } from '../../../lib/auth.js';

export async function POST(request) {
  try {
    // Get token from Authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'No token provided' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    // Invalidate the session
    const success = await invalidateSession(token);

    if (success) {
      return NextResponse.json({
        success: true,
        message: 'Logged out successfully'
      });
    } else {
      return NextResponse.json(
        { error: 'Failed to logout' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Logout error:', error);
    return NextResponse.json(
      { error: 'Logout failed. Please try again.' },
      { status: 500 }
    );
  }
}
