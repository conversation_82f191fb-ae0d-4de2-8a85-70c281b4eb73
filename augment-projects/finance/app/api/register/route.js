import { NextResponse } from 'next/server';
import prisma from '../../../lib/db.js';
import { hashPassword, generateToken, createSession, isValidEmail, validatePassword } from '../../../lib/auth.js';

export async function POST(request) {
  try {
    const body = await request.json();
    const { name, email, password, company, plan } = body;

    // Validate required fields
    if (!name || !email || !password) {
      return NextResponse.json(
        { error: 'Name, email, and password are required' },
        { status: 400 }
      );
    }

    // Validate email format
    if (!isValidEmail(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Validate password strength
    const passwordValidation = validatePassword(password);
    if (!passwordValidation.isValid) {
      return NextResponse.json(
        { error: passwordValidation.errors[0] },
        { status: 400 }
      );
    }

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: email.toLowerCase() }
    });

    if (existingUser) {
      return NextResponse.json(
        { error: 'User with this email already exists' },
        { status: 409 }
      );
    }

    // Hash password
    const hashedPassword = await hashPassword(password);

    // Determine plan (convert string to enum)
    let userPlan = 'FREE';
    if (plan) {
      const planUpper = plan.toUpperCase();
      if (['FREE', 'PRO', 'ENTERPRISE'].includes(planUpper)) {
        userPlan = planUpper;
      }
    }

    // Create user in database
    const user = await prisma.user.create({
      data: {
        name,
        email: email.toLowerCase(),
        password: hashedPassword,
        company: company || null,
        plan: userPlan,
      },
    });

    // Generate JWT token and create session
    const token = generateToken(user);
    await createSession(user.id, token);

    // Log registration for monitoring
    console.log('User registration:', {
      timestamp: new Date().toISOString(),
      userId: user.id,
      email: user.email,
      plan: user.plan
    });

    // Send welcome email (in production)
    if (process.env.NODE_ENV === 'production') {
      await sendWelcomeEmail(user);
    }

    // Return success response (don't include password)
    const { password: _, ...userResponse } = user;
    return NextResponse.json({
      success: true,
      message: 'Account created successfully!',
      user: userResponse,
      token,
      nextSteps: {
        dashboard: '/dashboard',
        firstScan: '/dashboard/scan',
        documentation: '/docs'
      }
    });

  } catch (error) {
    console.error('Registration error:', error);
    return NextResponse.json(
      { error: 'Failed to create account. Please try again.' },
      { status: 500 }
    );
  }
}

export async function GET(request) {
  // Get user count for admin purposes
  const url = new URL(request.url);
  const adminKey = url.searchParams.get('admin_key');

  if (adminKey !== process.env.ADMIN_KEY) {
    return NextResponse.json(
      { error: 'Unauthorized' },
      { status: 401 }
    );
  }

  try {
    // Get user statistics from database
    const totalUsers = await prisma.user.count();

    const planDistribution = await prisma.user.groupBy({
      by: ['plan'],
      _count: {
        plan: true,
      },
    });

    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
    const recentRegistrations = await prisma.user.count({
      where: {
        createdAt: {
          gte: oneDayAgo,
        },
      },
    });

    const userStats = {
      totalUsers,
      planDistribution: {
        FREE: 0,
        PRO: 0,
        ENTERPRISE: 0
      },
      recentRegistrations
    };

    // Format plan distribution
    planDistribution.forEach(item => {
      userStats.planDistribution[item.plan] = item._count.plan;
    });

    return NextResponse.json(userStats);
  } catch (error) {
    console.error('Error fetching user stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch user statistics' },
      { status: 500 }
    );
  }
}

async function sendWelcomeEmail(user) {
  // In production, implement actual email sending
  const welcomeEmailContent = {
    to: user.email,
    subject: 'Welcome to SecureAudit - Your Smart Contract Security Journey Begins!',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #1e293b 0%, #7c3aed 100%); padding: 20px; text-align: center;">
          <h1 style="color: white; margin: 0;">🛡️ Welcome to SecureAudit!</h1>
        </div>
        
        <div style="padding: 30px; background: #f8fafc; border: 1px solid #e2e8f0;">
          <h2 style="color: #1e293b; margin-top: 0;">Hi ${user.name},</h2>
          
          <p style="color: #374151; line-height: 1.6;">
            Welcome to SecureAudit! We're excited to help you secure your smart contracts with our AI-powered analysis platform.
          </p>
          
          <div style="background: #dbeafe; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #1e40af; margin: 0 0 15px 0;">Your Account Details</h3>
            <p style="color: #1e40af; margin: 0;"><strong>Plan:</strong> ${user.plan.charAt(0).toUpperCase() + user.plan.slice(1)}</p>
            <p style="color: #1e40af; margin: 5px 0 0 0;"><strong>Email:</strong> ${user.email}</p>
          </div>
          
          <h3 style="color: #1e293b;">Next Steps:</h3>
          <ol style="color: #374151; line-height: 1.6;">
            <li>Visit your <a href="https://secureaudit.com/dashboard" style="color: #3b82f6;">dashboard</a></li>
            <li>Upload your first smart contract for analysis</li>
            <li>Explore our <a href="https://secureaudit.com/docs" style="color: #3b82f6;">documentation</a></li>
            <li>Set up integrations with your development workflow</li>
          </ol>
          
          <div style="background: #f0fdf4; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #22c55e;">
            <h4 style="color: #15803d; margin: 0 0 10px 0;">🎉 Special Welcome Offer</h4>
            <p style="color: #15803d; margin: 0; font-size: 14px;">
              As a new user, you get <strong>3 free premium scans</strong> to try our advanced features!
            </p>
          </div>
          
          <p style="color: #374151; line-height: 1.6;">
            If you have any questions, our support team is here to help at 
            <a href="mailto:<EMAIL>" style="color: #3b82f6;"><EMAIL></a>
          </p>
          
          <p style="color: #374151; line-height: 1.6;">
            Best regards,<br>
            <strong>The SecureAudit Team</strong>
          </p>
        </div>
        
        <div style="padding: 20px; text-align: center; background: #1e293b;">
          <p style="color: #94a3b8; margin: 0 0 10px 0; font-size: 14px;">
            <strong>SecureAudit</strong> - AI-Powered Smart Contract Security
          </p>
          <p style="color: #64748b; margin: 0; font-size: 12px;">
            123 Security Blvd, San Francisco, CA 94105
          </p>
        </div>
      </div>
    `
  };

  console.log('Welcome email would be sent:', welcomeEmailContent);
}
