import { NextResponse } from 'next/server';
import { getUserFromToken } from '../../../../lib/auth.js';
import { getPlanConfig } from '../../../../lib/stripe.js';
import prisma from '../../../../lib/db.js';

export async function GET(request) {
  try {
    // Get user from token
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    const user = await getUserFromToken(token);

    if (!user) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      );
    }

    // Get user with subscription details
    const userWithSubscription = await prisma.user.findUnique({
      where: { id: user.id },
      include: {
        subscription: true,
        payments: {
          orderBy: { createdAt: 'desc' },
          take: 5
        }
      }
    });

    if (!userWithSubscription) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Get plan configuration
    const planConfig = getPlanConfig(userWithSubscription.plan);

    // Calculate usage for current month
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    
    const monthlyScans = await prisma.scan.count({
      where: {
        userId: user.id,
        createdAt: {
          gte: startOfMonth
        }
      }
    });

    // Check if user needs to reset monthly scan count
    const lastReset = new Date(userWithSubscription.lastScanReset);
    const shouldReset = lastReset < startOfMonth;

    if (shouldReset) {
      await prisma.user.update({
        where: { id: user.id },
        data: {
          monthlyScans: monthlyScans,
          lastScanReset: now
        }
      });
    }

    // Calculate limits and usage
    const scansLimit = planConfig.limits.scansPerMonth;
    const scansRemaining = scansLimit === -1 ? -1 : Math.max(0, scansLimit - monthlyScans);
    const scansUsagePercentage = scansLimit === -1 ? 0 : Math.min(100, (monthlyScans / scansLimit) * 100);

    // Determine if user can upgrade/downgrade
    const canUpgrade = userWithSubscription.plan !== 'ENTERPRISE';
    const canDowngrade = userWithSubscription.plan !== 'FREE';

    return NextResponse.json({
      success: true,
      subscription: {
        plan: userWithSubscription.plan,
        status: userWithSubscription.subscriptionStatus,
        currentPeriodEnd: userWithSubscription.currentPeriodEnd,
        cancelAtPeriodEnd: userWithSubscription.cancelAtPeriodEnd,
        stripeCustomerId: userWithSubscription.stripeCustomerId,
        hasActiveSubscription: userWithSubscription.subscriptionStatus === 'active'
      },
      usage: {
        monthlyScans: monthlyScans,
        scansLimit: scansLimit,
        scansRemaining: scansRemaining,
        scansUsagePercentage: Math.round(scansUsagePercentage)
      },
      planConfig: planConfig,
      billing: {
        canUpgrade: canUpgrade,
        canDowngrade: canDowngrade,
        hasPaymentMethod: !!userWithSubscription.stripeCustomerId,
        recentPayments: userWithSubscription.payments
      }
    });

  } catch (error) {
    console.error('Subscription status error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch subscription status' },
      { status: 500 }
    );
  }
}
