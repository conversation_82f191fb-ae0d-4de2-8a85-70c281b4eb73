import { NextResponse } from 'next/server';
import { getUserFromToken } from '../../../../lib/auth.js';
import { createStripeCustomer, createCheckoutSession, PLAN_CONFIG } from '../../../../lib/stripe.js';
import prisma from '../../../../lib/db.js';

export async function POST(request) {
  try {
    // Get user from token
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    const user = await getUserFromToken(token);

    if (!user) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { plan } = body;

    // Validate plan
    if (!plan || !PLAN_CONFIG[plan] || plan === 'FREE') {
      return NextResponse.json(
        { error: 'Invalid plan selected' },
        { status: 400 }
      );
    }

    const planConfig = PLAN_CONFIG[plan];
    if (!planConfig.stripePriceId) {
      return NextResponse.json(
        { error: 'Plan not available for purchase' },
        { status: 400 }
      );
    }

    // Check if user already has this plan or higher
    if (user.plan === plan) {
      return NextResponse.json(
        { error: 'You already have this plan' },
        { status: 400 }
      );
    }

    // Create Stripe customer if doesn't exist
    let stripeCustomerId = user.stripeCustomerId;
    if (!stripeCustomerId) {
      const customer = await createStripeCustomer(user);
      stripeCustomerId = customer.id;

      // Update user with Stripe customer ID
      await prisma.user.update({
        where: { id: user.id },
        data: { stripeCustomerId: stripeCustomerId }
      });
    }

    // Create checkout session
    const successUrl = `${request.headers.get('origin')}/dashboard?session_id={CHECKOUT_SESSION_ID}`;
    const cancelUrl = `${request.headers.get('origin')}/pricing?canceled=true`;

    const session = await createCheckoutSession(
      stripeCustomerId,
      planConfig.stripePriceId,
      successUrl,
      cancelUrl
    );

    // Log checkout session creation
    console.log('Checkout session created:', {
      timestamp: new Date().toISOString(),
      userId: user.id,
      email: user.email,
      plan: plan,
      sessionId: session.id
    });

    return NextResponse.json({
      success: true,
      sessionId: session.id,
      url: session.url
    });

  } catch (error) {
    console.error('Checkout session creation error:', error);
    return NextResponse.json(
      { error: 'Failed to create checkout session' },
      { status: 500 }
    );
  }
}
