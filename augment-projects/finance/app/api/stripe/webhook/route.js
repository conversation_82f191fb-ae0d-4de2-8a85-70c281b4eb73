import { NextResponse } from 'next/server';
import { constructWebhookEvent } from '../../../../lib/stripe.js';
import prisma from '../../../../lib/db.js';

// Disable body parsing for webhook
export const runtime = 'nodejs';

export async function POST(request) {
  try {
    const body = await request.text();
    const signature = request.headers.get('stripe-signature');

    if (!signature) {
      return NextResponse.json(
        { error: 'Missing stripe-signature header' },
        { status: 400 }
      );
    }

    // Construct the event
    const event = constructWebhookEvent(body, signature);

    console.log('Stripe webhook received:', {
      type: event.type,
      id: event.id,
      timestamp: new Date().toISOString()
    });

    // Handle the event
    switch (event.type) {
      case 'checkout.session.completed':
        await handleCheckoutSessionCompleted(event.data.object);
        break;

      case 'customer.subscription.created':
        await handleSubscriptionCreated(event.data.object);
        break;

      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(event.data.object);
        break;

      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event.data.object);
        break;

      case 'invoice.payment_succeeded':
        await handlePaymentSucceeded(event.data.object);
        break;

      case 'invoice.payment_failed':
        await handlePaymentFailed(event.data.object);
        break;

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return NextResponse.json({ received: true });

  } catch (error) {
    console.error('Webhook error:', error);
    return NextResponse.json(
      { error: 'Webhook handler failed' },
      { status: 400 }
    );
  }
}

async function handleCheckoutSessionCompleted(session) {
  try {
    console.log('Processing checkout session completed:', session.id);

    const customerId = session.customer;
    const subscriptionId = session.subscription;

    // Find user by Stripe customer ID
    const user = await prisma.user.findUnique({
      where: { stripeCustomerId: customerId }
    });

    if (!user) {
      console.error('User not found for customer:', customerId);
      return;
    }

    // Update user subscription status
    await prisma.user.update({
      where: { id: user.id },
      data: {
        subscriptionStatus: 'active'
      }
    });

    console.log('Checkout session processed successfully for user:', user.id);

  } catch (error) {
    console.error('Error handling checkout session completed:', error);
  }
}

async function handleSubscriptionCreated(subscription) {
  try {
    console.log('Processing subscription created:', subscription.id);

    const customerId = subscription.customer;
    const user = await prisma.user.findUnique({
      where: { stripeCustomerId: customerId }
    });

    if (!user) {
      console.error('User not found for customer:', customerId);
      return;
    }

    // Determine plan from price ID
    const priceId = subscription.items.data[0]?.price?.id;
    let plan = 'FREE';
    if (priceId === process.env.STRIPE_PRO_PRICE_ID) {
      plan = 'PRO';
    } else if (priceId === process.env.STRIPE_ENTERPRISE_PRICE_ID) {
      plan = 'ENTERPRISE';
    }

    // Create or update subscription record
    await prisma.subscription.upsert({
      where: { userId: user.id },
      update: {
        stripeSubscriptionId: subscription.id,
        stripePriceId: priceId,
        stripeProductId: subscription.items.data[0]?.price?.product,
        status: subscription.status.toUpperCase(),
        currentPeriodStart: new Date(subscription.current_period_start * 1000),
        currentPeriodEnd: new Date(subscription.current_period_end * 1000),
        cancelAtPeriodEnd: subscription.cancel_at_period_end,
        trialStart: subscription.trial_start ? new Date(subscription.trial_start * 1000) : null,
        trialEnd: subscription.trial_end ? new Date(subscription.trial_end * 1000) : null,
      },
      create: {
        userId: user.id,
        stripeSubscriptionId: subscription.id,
        stripePriceId: priceId,
        stripeProductId: subscription.items.data[0]?.price?.product,
        status: subscription.status.toUpperCase(),
        currentPeriodStart: new Date(subscription.current_period_start * 1000),
        currentPeriodEnd: new Date(subscription.current_period_end * 1000),
        cancelAtPeriodEnd: subscription.cancel_at_period_end,
        trialStart: subscription.trial_start ? new Date(subscription.trial_start * 1000) : null,
        trialEnd: subscription.trial_end ? new Date(subscription.trial_end * 1000) : null,
      }
    });

    // Update user plan and subscription status
    await prisma.user.update({
      where: { id: user.id },
      data: {
        plan: plan,
        subscriptionStatus: subscription.status,
        currentPeriodEnd: new Date(subscription.current_period_end * 1000),
        cancelAtPeriodEnd: subscription.cancel_at_period_end
      }
    });

    console.log('Subscription created successfully for user:', user.id, 'Plan:', plan);

  } catch (error) {
    console.error('Error handling subscription created:', error);
  }
}

async function handleSubscriptionUpdated(subscription) {
  try {
    console.log('Processing subscription updated:', subscription.id);

    // Find subscription in database
    const dbSubscription = await prisma.subscription.findUnique({
      where: { stripeSubscriptionId: subscription.id },
      include: { user: true }
    });

    if (!dbSubscription) {
      console.error('Subscription not found:', subscription.id);
      return;
    }

    // Update subscription record
    await prisma.subscription.update({
      where: { id: dbSubscription.id },
      data: {
        status: subscription.status.toUpperCase(),
        currentPeriodStart: new Date(subscription.current_period_start * 1000),
        currentPeriodEnd: new Date(subscription.current_period_end * 1000),
        cancelAtPeriodEnd: subscription.cancel_at_period_end,
        canceledAt: subscription.canceled_at ? new Date(subscription.canceled_at * 1000) : null,
      }
    });

    // Update user subscription status
    await prisma.user.update({
      where: { id: dbSubscription.userId },
      data: {
        subscriptionStatus: subscription.status,
        currentPeriodEnd: new Date(subscription.current_period_end * 1000),
        cancelAtPeriodEnd: subscription.cancel_at_period_end
      }
    });

    console.log('Subscription updated successfully for user:', dbSubscription.userId);

  } catch (error) {
    console.error('Error handling subscription updated:', error);
  }
}

async function handleSubscriptionDeleted(subscription) {
  try {
    console.log('Processing subscription deleted:', subscription.id);

    // Find subscription in database
    const dbSubscription = await prisma.subscription.findUnique({
      where: { stripeSubscriptionId: subscription.id }
    });

    if (!dbSubscription) {
      console.error('Subscription not found:', subscription.id);
      return;
    }

    // Update subscription status
    await prisma.subscription.update({
      where: { id: dbSubscription.id },
      data: {
        status: 'CANCELED',
        canceledAt: new Date()
      }
    });

    // Downgrade user to FREE plan
    await prisma.user.update({
      where: { id: dbSubscription.userId },
      data: {
        plan: 'FREE',
        subscriptionStatus: 'canceled'
      }
    });

    console.log('Subscription deleted successfully for user:', dbSubscription.userId);

  } catch (error) {
    console.error('Error handling subscription deleted:', error);
  }
}

async function handlePaymentSucceeded(invoice) {
  try {
    console.log('Processing payment succeeded:', invoice.id);

    const customerId = invoice.customer;
    const user = await prisma.user.findUnique({
      where: { stripeCustomerId: customerId }
    });

    if (!user) {
      console.error('User not found for customer:', customerId);
      return;
    }

    // Create payment record
    await prisma.payment.create({
      data: {
        userId: user.id,
        stripePaymentId: invoice.payment_intent,
        stripeInvoiceId: invoice.id,
        amount: invoice.amount_paid,
        currency: invoice.currency,
        status: 'SUCCEEDED',
        description: invoice.description || `Payment for ${invoice.lines.data[0]?.description}`
      }
    });

    console.log('Payment succeeded processed for user:', user.id);

  } catch (error) {
    console.error('Error handling payment succeeded:', error);
  }
}

async function handlePaymentFailed(invoice) {
  try {
    console.log('Processing payment failed:', invoice.id);

    const customerId = invoice.customer;
    const user = await prisma.user.findUnique({
      where: { stripeCustomerId: customerId }
    });

    if (!user) {
      console.error('User not found for customer:', customerId);
      return;
    }

    // Create payment record
    await prisma.payment.create({
      data: {
        userId: user.id,
        stripePaymentId: invoice.payment_intent,
        stripeInvoiceId: invoice.id,
        amount: invoice.amount_due,
        currency: invoice.currency,
        status: 'FAILED',
        description: invoice.description || `Failed payment for ${invoice.lines.data[0]?.description}`
      }
    });

    // Update user subscription status if needed
    await prisma.user.update({
      where: { id: user.id },
      data: {
        subscriptionStatus: 'past_due'
      }
    });

    console.log('Payment failed processed for user:', user.id);

  } catch (error) {
    console.error('Error handling payment failed:', error);
  }
}
