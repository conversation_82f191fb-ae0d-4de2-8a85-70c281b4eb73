import { NextResponse } from 'next/server';
import { getUserFromToken } from '../../../../lib/auth.js';
import { createPortalSession } from '../../../../lib/stripe.js';

export async function POST(request) {
  try {
    // Get user from token
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    const user = await getUserFromToken(token);

    if (!user) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      );
    }

    // Check if user has a Stripe customer ID
    if (!user.stripeCustomerId) {
      return NextResponse.json(
        { error: 'No billing account found' },
        { status: 400 }
      );
    }

    // Create portal session
    const returnUrl = `${request.headers.get('origin')}/dashboard`;
    const session = await createPortalSession(user.stripeCustomerId, returnUrl);

    // Log portal session creation
    console.log('Customer portal session created:', {
      timestamp: new Date().toISOString(),
      userId: user.id,
      email: user.email,
      sessionId: session.id
    });

    return NextResponse.json({
      success: true,
      url: session.url
    });

  } catch (error) {
    console.error('Customer portal session creation error:', error);
    return NextResponse.json(
      { error: 'Failed to create portal session' },
      { status: 500 }
    );
  }
}
