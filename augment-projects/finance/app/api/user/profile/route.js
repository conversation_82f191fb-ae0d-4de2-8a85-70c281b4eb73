import { NextResponse } from 'next/server';
import prisma from '../../../../lib/db.js';
import { getUserFromToken, hashPassword, isValidEmail } from '../../../../lib/auth.js';

// Get user profile
export async function GET(request) {
  try {
    // Get token from Authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'No token provided' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    const user = await getUserFromToken(token);

    if (!user) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      );
    }

    // Get user with scan statistics
    const userWithStats = await prisma.user.findUnique({
      where: { id: user.id },
      include: {
        scans: {
          select: {
            id: true,
            fileName: true,
            securityScore: true,
            riskLevel: true,
            analysisTimestamp: true,
          },
          orderBy: {
            analysisTimestamp: 'desc',
          },
          take: 10, // Last 10 scans
        },
        _count: {
          select: {
            scans: true,
          },
        },
      },
    });

    if (!userWithStats) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Calculate statistics
    const totalScans = userWithStats._count.scans;
    const recentScans = userWithStats.scans;
    
    let totalVulnerabilities = 0;
    let totalSecurityScore = 0;
    
    if (totalScans > 0) {
      const allScans = await prisma.scan.findMany({
        where: { userId: user.id },
        select: {
          vulnerabilities: true,
          securityScore: true,
        },
      });

      totalVulnerabilities = allScans.reduce((sum, scan) => {
        const vulns = Array.isArray(scan.vulnerabilities) ? scan.vulnerabilities : [];
        return sum + vulns.length;
      }, 0);

      totalSecurityScore = Math.round(
        allScans.reduce((sum, scan) => sum + scan.securityScore, 0) / allScans.length
      );
    }

    // Remove password from response
    const { password: _, ...userResponse } = userWithStats;

    return NextResponse.json({
      success: true,
      user: userResponse,
      statistics: {
        totalScans,
        totalVulnerabilities,
        averageSecurityScore: totalSecurityScore,
        recentScans,
      },
    });

  } catch (error) {
    console.error('Profile fetch error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch profile' },
      { status: 500 }
    );
  }
}

// Update user profile
export async function PUT(request) {
  try {
    // Get token from Authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'No token provided' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    const user = await getUserFromToken(token);

    if (!user) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { name, email, company, currentPassword, newPassword } = body;

    // Validate required fields
    if (!name || !email) {
      return NextResponse.json(
        { error: 'Name and email are required' },
        { status: 400 }
      );
    }

    // Validate email format
    if (!isValidEmail(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Check if email is already taken by another user
    if (email.toLowerCase() !== user.email) {
      const existingUser = await prisma.user.findUnique({
        where: { email: email.toLowerCase() }
      });

      if (existingUser) {
        return NextResponse.json(
          { error: 'Email is already taken' },
          { status: 409 }
        );
      }
    }

    // Prepare update data
    const updateData = {
      name,
      email: email.toLowerCase(),
      company: company || null,
    };

    // Handle password change
    if (newPassword) {
      if (!currentPassword) {
        return NextResponse.json(
          { error: 'Current password is required to set new password' },
          { status: 400 }
        );
      }

      // Verify current password
      const { verifyPassword } = await import('../../../../lib/auth.js');
      const isCurrentPasswordValid = await verifyPassword(currentPassword, user.password);
      
      if (!isCurrentPasswordValid) {
        return NextResponse.json(
          { error: 'Current password is incorrect' },
          { status: 400 }
        );
      }

      // Hash new password
      updateData.password = await hashPassword(newPassword);
    }

    // Update user in database
    const updatedUser = await prisma.user.update({
      where: { id: user.id },
      data: updateData,
    });

    // Remove password from response
    const { password: _, ...userResponse } = updatedUser;

    return NextResponse.json({
      success: true,
      message: 'Profile updated successfully',
      user: userResponse,
    });

  } catch (error) {
    console.error('Profile update error:', error);
    return NextResponse.json(
      { error: 'Failed to update profile' },
      { status: 500 }
    );
  }
}
