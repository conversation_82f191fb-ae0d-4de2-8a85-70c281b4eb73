'use client';

import { useState, useEffect } from 'react';

export default function Dashboard() {
  const [user, setUser] = useState(null);
  const [statistics, setStatistics] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchUserProfile();
  }, []);

  const fetchUserProfile = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Get token from localStorage
      const token = localStorage.getItem('auth-token');
      if (!token) {
        // Redirect to login if no token
        window.location.href = '/login';
        return;
      }

      const response = await fetch('/api/user/profile', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          // Token expired or invalid
          localStorage.removeItem('auth-token');
          window.location.href = '/login';
          return;
        }
        throw new Error('Failed to fetch profile');
      }

      const data = await response.json();
      setUser(data.user);
      setStatistics(data.statistics);

    } catch (error) {
      console.error('Error fetching profile:', error);
      setError('Failed to load dashboard data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      const token = localStorage.getItem('auth-token');
      if (token) {
        await fetch('/api/logout', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      localStorage.removeItem('auth-token');
      window.location.href = '/';
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-white">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-400 text-6xl mb-4">⚠️</div>
          <p className="text-white text-xl mb-4">{error}</p>
          <button
            onClick={fetchUserProfile}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <p className="text-white">No user data available</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Navigation */}
      <nav className="container mx-auto px-6 py-4 border-b border-slate-700">
        <div className="flex items-center justify-between">
          <a href="/" className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-lg">🛡️</span>
            </div>
            <span className="text-white font-bold text-xl">SecureAudit</span>
          </a>
          <div className="flex items-center space-x-6">
            <span className="text-gray-300">Welcome, {user.name}</span>
            <button
              onClick={handleLogout}
              className="bg-slate-700 text-white px-4 py-2 rounded-lg hover:bg-slate-600 transition-colors"
            >
              Sign Out
            </button>
          </div>
        </div>
      </nav>

      <div className="container mx-auto px-6 py-8">
        <div className="grid lg:grid-cols-4 gap-8">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-slate-800/50 backdrop-blur-sm p-6 rounded-xl border border-slate-700">
              <h3 className="text-lg font-bold text-white mb-4">Quick Actions</h3>
              <div className="space-y-3">
                <button className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all">
                  New Scan
                </button>
                <button className="w-full bg-slate-700 text-white py-3 rounded-lg hover:bg-slate-600 transition-colors">
                  View Reports
                </button>
                <button className="w-full bg-slate-700 text-white py-3 rounded-lg hover:bg-slate-600 transition-colors">
                  API Keys
                </button>
                <button className="w-full bg-slate-700 text-white py-3 rounded-lg hover:bg-slate-600 transition-colors">
                  Settings
                </button>
              </div>
            </div>

            {/* Plan Info */}
            <div className="bg-slate-800/50 backdrop-blur-sm p-6 rounded-xl border border-slate-700 mt-6">
              <h3 className="text-lg font-bold text-white mb-4">Current Plan</h3>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-400 mb-2">{user.plan}</div>
                <p className="text-gray-300 text-sm mb-4">
                  {user.plan === 'FREE' ? 'Limited features' : 'Full access'}
                </p>
                {user.plan === 'FREE' && (
                  <button className="w-full bg-gradient-to-r from-green-600 to-blue-600 text-white py-2 rounded-lg text-sm hover:from-green-700 hover:to-blue-700 transition-all">
                    Upgrade to Pro
                  </button>
                )}
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            {/* Stats Cards */}
            <div className="grid md:grid-cols-3 gap-6 mb-8">
              <div className="bg-slate-800/50 backdrop-blur-sm p-6 rounded-xl border border-slate-700">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-gray-400 text-sm">Total Scans</p>
                    <p className="text-2xl font-bold text-white">{statistics?.totalScans || 0}</p>
                  </div>
                  <div className="text-3xl">📊</div>
                </div>
              </div>
              <div className="bg-slate-800/50 backdrop-blur-sm p-6 rounded-xl border border-slate-700">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-gray-400 text-sm">Vulnerabilities Found</p>
                    <p className="text-2xl font-bold text-yellow-400">{statistics?.totalVulnerabilities || 0}</p>
                  </div>
                  <div className="text-3xl">⚠️</div>
                </div>
              </div>
              <div className="bg-slate-800/50 backdrop-blur-sm p-6 rounded-xl border border-slate-700">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-gray-400 text-sm">Avg Security Score</p>
                    <p className="text-2xl font-bold text-green-400">{statistics?.averageSecurityScore || 0}</p>
                  </div>
                  <div className="text-3xl">🛡️</div>
                </div>
              </div>
            </div>

            {/* Recent Scans */}
            <div className="bg-slate-800/50 backdrop-blur-sm p-6 rounded-xl border border-slate-700">
              <h3 className="text-xl font-bold text-white mb-6">Recent Scans</h3>
              <div className="space-y-4">
                {statistics?.recentScans && statistics.recentScans.length > 0 ? (
                  statistics.recentScans.map((scan) => (
                    <div key={scan.id} className="bg-slate-700/50 p-4 rounded-lg border border-slate-600">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <h4 className="text-white font-semibold">{scan.fileName}</h4>
                          <p className="text-gray-400 text-sm">
                            {new Date(scan.analysisTimestamp).toLocaleDateString()} •
                            Score: {scan.securityScore}/100
                          </p>
                        </div>
                        <div className="flex items-center space-x-4">
                          <span className={`px-3 py-1 rounded-full text-sm font-semibold ${
                            scan.riskLevel === 'LOW' ? 'bg-green-900/30 text-green-400' :
                            scan.riskLevel === 'MEDIUM' ? 'bg-yellow-900/30 text-yellow-400' :
                            scan.riskLevel === 'HIGH' ? 'bg-red-900/30 text-red-400' :
                            'bg-red-900/50 text-red-300'
                          }`}>
                            {scan.riskLevel}
                          </span>
                          <button className="text-blue-400 hover:text-blue-300 transition-colors">
                            View Report
                          </button>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8">
                    <div className="text-gray-400 text-4xl mb-4">📄</div>
                    <p className="text-gray-400">No scans yet</p>
                    <p className="text-gray-500 text-sm">Upload your first smart contract to get started</p>
                  </div>
                )}
              </div>
              
              <div className="mt-6 text-center">
                <button className="text-blue-400 hover:text-blue-300 transition-colors">
                  View All Scans →
                </button>
              </div>
            </div>

            {/* Getting Started */}
            <div className="bg-slate-800/50 backdrop-blur-sm p-6 rounded-xl border border-slate-700 mt-8">
              <h3 className="text-xl font-bold text-white mb-4">Getting Started</h3>
              <div className="grid md:grid-cols-2 gap-6">
                <div className="bg-slate-700/30 p-4 rounded-lg">
                  <h4 className="text-white font-semibold mb-2">📚 Read Documentation</h4>
                  <p className="text-gray-300 text-sm mb-3">
                    Learn how to integrate SecureAudit with your development workflow.
                  </p>
                  <button className="text-blue-400 hover:text-blue-300 transition-colors text-sm">
                    View Docs →
                  </button>
                </div>
                <div className="bg-slate-700/30 p-4 rounded-lg">
                  <h4 className="text-white font-semibold mb-2">🔧 Setup Integrations</h4>
                  <p className="text-gray-300 text-sm mb-3">
                    Connect with GitHub Actions, VS Code, and other tools.
                  </p>
                  <button className="text-blue-400 hover:text-blue-300 transition-colors text-sm">
                    Setup →
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
