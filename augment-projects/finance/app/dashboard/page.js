'use client';

import { useState, useEffect } from 'react';

export default function Dashboard() {
  const [user, setUser] = useState(null);
  const [scanHistory, setScanHistory] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate loading user data
    setTimeout(() => {
      setUser({
        name: 'Demo User',
        email: '<EMAIL>',
        plan: 'pro',
        scanCount: 12,
        joinDate: '2024-01-15'
      });
      
      setScanHistory([
        {
          id: 'SA_1701234567_abc123',
          fileName: 'TokenContract.sol',
          timestamp: '2024-12-01T10:30:00Z',
          riskLevel: 'Low',
          vulnerabilities: 1,
          score: 92
        },
        {
          id: 'SA_1701234500_def456',
          fileName: 'DeFiProtocol.sol',
          timestamp: '2024-11-30T15:45:00Z',
          riskLevel: 'Medium',
          vulnerabilities: 3,
          score: 78
        },
        {
          id: 'SA_1701234400_ghi789',
          fileName: 'NFTMarketplace.sol',
          timestamp: '2024-11-29T09:15:00Z',
          riskLevel: 'High',
          vulnerabilities: 5,
          score: 65
        }
      ]);
      
      setIsLoading(false);
    }, 1000);
  }, []);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-white">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Navigation */}
      <nav className="container mx-auto px-6 py-4 border-b border-slate-700">
        <div className="flex items-center justify-between">
          <a href="/" className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-lg">🛡️</span>
            </div>
            <span className="text-white font-bold text-xl">SecureAudit</span>
          </a>
          <div className="flex items-center space-x-6">
            <span className="text-gray-300">Welcome, {user.name}</span>
            <button className="bg-slate-700 text-white px-4 py-2 rounded-lg hover:bg-slate-600 transition-colors">
              Sign Out
            </button>
          </div>
        </div>
      </nav>

      <div className="container mx-auto px-6 py-8">
        <div className="grid lg:grid-cols-4 gap-8">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-slate-800/50 backdrop-blur-sm p-6 rounded-xl border border-slate-700">
              <h3 className="text-lg font-bold text-white mb-4">Quick Actions</h3>
              <div className="space-y-3">
                <button className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all">
                  New Scan
                </button>
                <button className="w-full bg-slate-700 text-white py-3 rounded-lg hover:bg-slate-600 transition-colors">
                  View Reports
                </button>
                <button className="w-full bg-slate-700 text-white py-3 rounded-lg hover:bg-slate-600 transition-colors">
                  API Keys
                </button>
                <button className="w-full bg-slate-700 text-white py-3 rounded-lg hover:bg-slate-600 transition-colors">
                  Settings
                </button>
              </div>
            </div>

            {/* Plan Info */}
            <div className="bg-slate-800/50 backdrop-blur-sm p-6 rounded-xl border border-slate-700 mt-6">
              <h3 className="text-lg font-bold text-white mb-4">Current Plan</h3>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-400 mb-2">{user.plan.toUpperCase()}</div>
                <p className="text-gray-300 text-sm mb-4">
                  {user.plan === 'free' ? 'Limited features' : 'Full access'}
                </p>
                {user.plan === 'free' && (
                  <button className="w-full bg-gradient-to-r from-green-600 to-blue-600 text-white py-2 rounded-lg text-sm hover:from-green-700 hover:to-blue-700 transition-all">
                    Upgrade to Pro
                  </button>
                )}
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            {/* Stats Cards */}
            <div className="grid md:grid-cols-3 gap-6 mb-8">
              <div className="bg-slate-800/50 backdrop-blur-sm p-6 rounded-xl border border-slate-700">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-gray-400 text-sm">Total Scans</p>
                    <p className="text-2xl font-bold text-white">{user.scanCount}</p>
                  </div>
                  <div className="text-3xl">📊</div>
                </div>
              </div>
              <div className="bg-slate-800/50 backdrop-blur-sm p-6 rounded-xl border border-slate-700">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-gray-400 text-sm">Vulnerabilities Found</p>
                    <p className="text-2xl font-bold text-yellow-400">23</p>
                  </div>
                  <div className="text-3xl">⚠️</div>
                </div>
              </div>
              <div className="bg-slate-800/50 backdrop-blur-sm p-6 rounded-xl border border-slate-700">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-gray-400 text-sm">Avg Security Score</p>
                    <p className="text-2xl font-bold text-green-400">85</p>
                  </div>
                  <div className="text-3xl">🛡️</div>
                </div>
              </div>
            </div>

            {/* Recent Scans */}
            <div className="bg-slate-800/50 backdrop-blur-sm p-6 rounded-xl border border-slate-700">
              <h3 className="text-xl font-bold text-white mb-6">Recent Scans</h3>
              <div className="space-y-4">
                {scanHistory.map((scan) => (
                  <div key={scan.id} className="bg-slate-700/50 p-4 rounded-lg border border-slate-600">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <h4 className="text-white font-semibold">{scan.fileName}</h4>
                        <p className="text-gray-400 text-sm">
                          {new Date(scan.timestamp).toLocaleDateString()} • 
                          {scan.vulnerabilities} vulnerabilities • 
                          Score: {scan.score}/100
                        </p>
                      </div>
                      <div className="flex items-center space-x-4">
                        <span className={`px-3 py-1 rounded-full text-sm font-semibold ${
                          scan.riskLevel === 'Low' ? 'bg-green-900/30 text-green-400' :
                          scan.riskLevel === 'Medium' ? 'bg-yellow-900/30 text-yellow-400' :
                          'bg-red-900/30 text-red-400'
                        }`}>
                          {scan.riskLevel}
                        </span>
                        <button className="text-blue-400 hover:text-blue-300 transition-colors">
                          View Report
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="mt-6 text-center">
                <button className="text-blue-400 hover:text-blue-300 transition-colors">
                  View All Scans →
                </button>
              </div>
            </div>

            {/* Getting Started */}
            <div className="bg-slate-800/50 backdrop-blur-sm p-6 rounded-xl border border-slate-700 mt-8">
              <h3 className="text-xl font-bold text-white mb-4">Getting Started</h3>
              <div className="grid md:grid-cols-2 gap-6">
                <div className="bg-slate-700/30 p-4 rounded-lg">
                  <h4 className="text-white font-semibold mb-2">📚 Read Documentation</h4>
                  <p className="text-gray-300 text-sm mb-3">
                    Learn how to integrate SecureAudit with your development workflow.
                  </p>
                  <button className="text-blue-400 hover:text-blue-300 transition-colors text-sm">
                    View Docs →
                  </button>
                </div>
                <div className="bg-slate-700/30 p-4 rounded-lg">
                  <h4 className="text-white font-semibold mb-2">🔧 Setup Integrations</h4>
                  <p className="text-gray-300 text-sm mb-3">
                    Connect with GitHub Actions, VS Code, and other tools.
                  </p>
                  <button className="text-blue-400 hover:text-blue-300 transition-colors text-sm">
                    Setup →
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
